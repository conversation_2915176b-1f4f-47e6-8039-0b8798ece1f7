version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:2025.05
    container_name: uniq_user_neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_directories_import=/import
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
      - neo4j_plugins:/plugins
    networks:
      - uniq_user_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7474"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Unified Application (API + UI)
  app:
    build: .
    container_name: uniq_user_app
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
      - SERVICES=api,ui
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    ports:
      - "8000:8000"  # FastAPI
      - "8501:8501"  # Streamlit
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Only Service (optional)
  api-only:
    build: .
    container_name: uniq_user_api_only
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
      - SERVICES=api
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    ports:
      - "8001:8000"  # FastAPI on different port
    profiles:
      - api-only

  # UI Only Service (optional)
  ui-only:
    build: .
    container_name: uniq_user_ui_only
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
      - SERVICES=ui
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    ports:
      - "8502:8501"  # Streamlit on different port
    profiles:
      - ui-only

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local

networks:
  uniq_user_network:
    driver: bridge 