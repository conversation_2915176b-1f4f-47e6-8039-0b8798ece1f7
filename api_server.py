#!/usr/bin/env python3
"""
FastAPI server for the One ID Mapping System.
"""

import logging
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.api_gateway import APIService
from src.api_gateway.models import (
    DataIngestionRequest,
    DataIngestionResponse,
    PipelineExecutionRequest,
    PipelineExecutionResponse,
    DataCleaningResponse,
    DatabaseStatsResponse,
    PipelineStatusResponse,
    HealthCheckResponse,
    CSVProcessingRequest,
    CSVProcessingResponse,
    CSVValidationRequest,
    CSVValidationResponse,
    CSVInfoResponse,
    RawDataProcessingRequest,
    RawDataProcessingResponse
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global API service instance
api_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    global api_service
    
    # Startup
    logger.info("Starting API server...")
    api_service = APIService()
    
    # Initialize the API service
    success = await api_service.initialize()
    if not success:
        logger.error("Failed to initialize API service")
        raise RuntimeError("API service initialization failed")
    
    logger.info("API server started successfully")
    yield
    
    # Shutdown
    logger.info("Shutting down API server...")
    if api_service:
        await api_service.cleanup()
    logger.info("API server shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="One ID Mapping System API",
    description="REST API for the One ID Mapping System - Data processing and user merging pipeline",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_model=dict)
async def root():
    """Root endpoint with API information."""
    return {
        "message": "One ID Mapping System API",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "ingest": "/ingest",
            "pipeline": "/pipeline",
            "clean": "/clean",
            "stats": "/stats",
            "status": "/status",
            "csv": {
                "process": "/csv/process",
                "process-raw": "/csv/process-raw",
                "validate": "/csv/validate",
                "info": "/csv/info",
                "columns": "/csv/columns"
            }
        }
    }


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    return await api_service.health_check()


@app.post("/ingest", response_model=DataIngestionResponse)
async def ingest_data(request: DataIngestionRequest):
    """Ingest data from CSV files."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.ingest_data(request)
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline", response_model=PipelineExecutionResponse)
async def execute_pipeline(request: PipelineExecutionRequest):
    """Execute the data processing pipeline."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.execute_pipeline(request)
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/clean", response_model=DataCleaningResponse)
async def clean_data():
    """Clean all data from the database."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.clean_data()
    except Exception as e:
        logger.error(f"Data cleaning failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/stats", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get database statistics."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.get_database_stats()
    except Exception as e:
        logger.error(f"Failed to get database stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/status", response_model=PipelineStatusResponse)
async def get_pipeline_status():
    """Get pipeline status."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.get_pipeline_status()
    except Exception as e:
        logger.error(f"Failed to get pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/configure-source")
async def configure_source(source_id: str, file_path: str, columns: list[str] = None):
    """Configure a data source for processing."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        api_service.ingestion_service.configure_csv_source(
            source_id=source_id,
            file_path=file_path,
            default_columns=columns
        )
        return {
            "success": True,
            "message": f"Source '{source_id}' configured successfully",
            "file_path": file_path,
            "columns": columns
        }
    except Exception as e:
        logger.error(f"Source configuration failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/sources")
async def list_sources():
    """List all configured data sources."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        sources = list(api_service.ingestion_service._source_configs.keys())
        return {
            "sources": sources,
            "count": len(sources)
        }
    except Exception as e:
        logger.error(f"Failed to list sources: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# CSV Processing Endpoints

@app.post("/csv/process", response_model=CSVProcessingResponse)
async def process_csv(request: CSVProcessingRequest):
    """Process a CSV file and return account data."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.process_csv(request)
    except Exception as e:
        logger.error(f"CSV processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/csv/process-raw", response_model=RawDataProcessingResponse)
async def process_raw_data(request: RawDataProcessingRequest):
    """Process all CSV files from raw_data directory and save to processed_data."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.process_raw_data(request)
    except Exception as e:
        logger.error(f"Raw data processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/csv/validate", response_model=CSVValidationResponse)
async def validate_csv(request: CSVValidationRequest):
    """Validate a CSV file."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.validate_csv(request)
    except Exception as e:
        logger.error(f"CSV validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/csv/info", response_model=CSVInfoResponse)
async def get_csv_info(file_path: str):
    """Get information about a CSV file."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        return await api_service.get_csv_info(file_path)
    except Exception as e:
        logger.error(f"Failed to get CSV info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/csv/columns")
async def get_csv_columns(file_path: str):
    """Get column names from a CSV file."""
    if not api_service:
        raise HTTPException(status_code=503, detail="API service not initialized")
    
    try:
        import csv
        from pathlib import Path
        
        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            raise HTTPException(status_code=404, detail=f"File not found: {file_path}")
        
        with open(file_path_obj, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            columns = reader.fieldnames or []
        
        return {
            "file_path": file_path,
            "columns": columns,
            "column_count": len(columns)
        }
    except Exception as e:
        logger.error(f"Failed to get CSV columns: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc)}
    )


if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8008,
        reload=True,
        log_level="info"
    )