# One ID Mapping System

A graph-based user identity resolution platform designed for gaming companies with multiple products. The system solves the critical problem of fragmented user identities across different gaming products by creating a unified user profile through intelligent account merging and attribute aggregation.

## Overview

The One ID Mapping System is built as a microservices-based platform with Neo4j as the primary graph database for user identity resolution. It provides:

- **Multi-Source Data Integration**: Ingests user data from multiple gaming products and external systems
- **Intelligent User Merging**: Automatically identifies and merges multiple accounts belonging to the same user
- **Attribute Aggregation**: Intelligently combines user attributes from multiple accounts
- **Graph Database Storage**: Stores user relationships and attributes in Neo4j for efficient querying
- **API Gateway**: Provides REST and GraphQL APIs for data access

## Project Structure

```
uniq_user_server/
├── src/
│   ├── config/                 # Configuration management
│   │   ├── settings.py         # Main application settings
│   │   ├── database_config.py  # Database configurations
│   │   └── processing_config.py # Processing rules and configurations
│   ├── data_ingestion/         # Data ingestion services
│   │   ├── csv_processor.py    # CSV file processing
│   │   ├── database_connector.py # Database connections
│   │   ├── data_validator.py   # Data validation
│   │   └── ingestion_service.py # Main ingestion orchestration
│   ├── user_merging/           # User merging services
│   │   ├── email_merger.py     # Email-based merging algorithm
│   │   ├── merge_executor.py   # Merge execution in graph database
│   │   └── merging_service.py  # Main merging orchestration
│   ├── attribute_aggregation/  # Attribute aggregation services
│   ├── graph_service/          # Graph database services
│   │   ├── graph_data_ingest.py    # Neo4j operations
│   │   └── graph_query_service.py # Graph queries
│   ├── api_gateway/            # API services
│   ├── data_pipeline/          # Data processing pipeline
│   │   └── pipeline.py         # Main pipeline orchestration
│   ├── models/                 # Data models
│   │   ├── account_data.py     # Account data structures
│   │   ├── user_data.py        # User data structures
│   │   ├── merge_data.py       # Merge operation structures
│   │   ├── financial_metrics.py # Financial metrics
│   │   └── api_models.py       # API request/response models
│   └── utils/                  # Utility functions
├── tests/                      # Test files
├── docs/                       # Documentation
├── design/                     # Design documents
├── main.py                     # Application entry point
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## Core Modules

### 1. Data Ingestion Service

Handles data ingestion from multiple sources and prepares data for processing.

**Key Components:**
- `CSVDataProcessor`: Processes CSV files and converts them to AccountData objects
- `DatabaseConnector`: Connects to various databases and fetches account data
- `DataValidator`: Validates account data for quality and consistency
- `DataIngestionService`: Main service that orchestrates data ingestion

**Key Functions:**
- Process CSV files with automatic data validation
- Connect to PostgreSQL/MySQL databases
- Validate data quality and business rules
- Transform data into standardized AccountData format

### 2. User Merging Service

Identifies and merges multiple accounts belonging to the same user.

**Key Components:**
- `EmailBasedMerger`: Identifies merge candidates based on email addresses
- `MergeExecutor`: Executes merge operations in the graph database
- `UserMergingService`: Main service that orchestrates merging operations

**Key Functions:**
- Email-based account grouping with confidence scoring
- Time proximity and financial behavior analysis
- Merge validation and execution
- Batch merge operations with error handling

### 3. Graph Database Service

Manages Neo4j graph database operations and complex queries.

**Key Components:**
- `GraphDatabaseManager`: Manages Neo4j connections and basic operations
- `GraphQueryService`: Provides complex graph queries and analytics

**Key Functions:**
- Create and manage user, account, and email nodes
- Establish relationships between entities
- Execute complex graph queries for user discovery
- Provide user insights and analytics

### 4. Data Processing Pipeline

Orchestrates the entire data processing workflow.

**Key Functions:**
- Coordinate data ingestion, merging, and storage
- Provide pipeline status and monitoring
- Support preview operations without committing changes
- Handle batch processing with error recovery

## Data Models

### AccountData
Represents account data from various sources with fields like:
- `account_id`: Unique account identifier
- `email`: User email address
- `deposit_amount_sum`: Total deposits
- `bet_money_sum`: Total bets
- `withdraw_amount_sum`: Total withdrawals
- `reg_time`: Registration time
- `active_time`: Last activity time
- `source_id`: Data source identifier

### UserData
Represents aggregated user data with fields like:
- `user_id`: Unique user identifier
- `total_deposits`: Aggregated deposits across all accounts
- `total_bets`: Aggregated bets across all accounts
- `account_count`: Number of accounts for this user
- `email_count`: Number of unique emails

### MergeGroup
Represents a group of accounts to be merged with:
- `accounts`: List of accounts to merge
- `confidence_score`: Confidence in the merge (0.0-1.0)
- `merge_criteria`: Criteria used for merging (e.g., "email")
- `merge_id`: Unique merge operation identifier

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd uniq_user_server
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   Create a `.env` file with the following variables:
   ```env
   # Neo4j Configuration
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USERNAME=neo4j
   NEO4J_PASSWORD=password
   
   # Application Settings
   SECRET_KEY=your-secret-key
   DEBUG=False
   LOG_LEVEL=INFO
   
   # Processing Configuration
   MERGE_CONFIDENCE_THRESHOLD=0.8
   BATCH_SIZE=1000
   ```

4. **Set up Neo4j database:**
   - Install Neo4j Desktop or Neo4j Community Edition
   - Create a new database
   - Update the connection details in your `.env` file

## Usage

### Basic Usage

```python
import asyncio
from src.config.settings import get_settings
from src.config.database_config import Neo4jConfig
from src.config.processing_config import ProcessingConfig
from src.data_ingestion.ingestion_service import DataIngestionService
from src.user_merging.merging_service import UserMergingService
from src.graph_service.graph_data_ingest import GraphDataIngest
from src.data_pipeline.pipeline import DataProcessingPipeline

async def main():
    # Load settings
    settings = get_settings()
    
    # Initialize Neo4j configuration
    neo4j_config = Neo4jConfig(
        uri=settings.neo4j_uri,
        username=settings.neo4j_username,
        password=settings.neo4j_password
    )
    
    # Initialize services
    processing_config = ProcessingConfig()
    graph_manager = GraphDataIngest(neo4j_config, processing_config)
    await graph_manager.connect()
    
    ingestion_service = DataIngestionService()
    merging_service = UserMergingService(
        graph_manager=graph_manager,
        confidence_threshold=settings.merge_confidence_threshold
    )
    
    pipeline = DataProcessingPipeline(
        ingestion_service=ingestion_service,
        merging_service=merging_service,
        graph_manager=graph_manager
    )
    
    # Configure and process CSV source
    ingestion_service.configure_csv_source("example", "path/to/data.csv")
    result = await pipeline.process_data_source("example")
    
    print(f"Pipeline result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Processing CSV Data

```python
from src.data_ingestion.ingestion_service import DataIngestionService

# Initialize service
ingestion_service = DataIngestionService()

# Configure CSV source
ingestion_service.configure_csv_source("gaming_data", "data/users.csv")

# Process the data
accounts = await ingestion_service.process_source("gaming_data")
print(f"Processed {len(accounts)} accounts")
```

### User Merging

```python
from src.user_merging.merging_service import UserMergingService

# Initialize merging service
merging_service = UserMergingService(graph_manager, confidence_threshold=0.8)

# Find merge candidates
merge_groups = await merging_service.find_merge_candidates(accounts)
print(f"Found {len(merge_groups)} merge groups")

# Execute merging
results = await merging_service.execute_merging("source_id", accounts)
```

## Configuration

### Processing Configuration

The system supports various configuration options:

```python
from src.config.processing_config import ProcessingConfig, AggregationRules

# Processing settings
config = ProcessingConfig(
    batch_size=1000,
    max_workers=4,
    merge_confidence_threshold=0.8,
    enable_parallel_processing=True
)

# Aggregation rules
rules = AggregationRules()
rules.add_rule('deposit_amount_sum', AggregationRule('sum', 'financial'))
rules.add_rule('reg_time', AggregationRule('min', 'time'))
```

### Database Configuration

```python
from src.config.database_config import Neo4jConfig

neo4j_config = Neo4jConfig(
    uri="bolt://localhost:7687",
    username="neo4j",
    password="password",
    max_connection_pool_size=50,
    connection_timeout=30
)
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test module
pytest tests/test_data_ingestion.py
```

## Development

### Code Style

The project uses:
- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking

```bash
# Format code
black src/

# Sort imports
isort src/

# Run linting
flake8 src/

# Type checking
mypy src/
```

### Adding New Features

1. **Create new modules** in the appropriate package
2. **Add tests** for new functionality
3. **Update documentation** and README
4. **Follow the existing code structure** and patterns

## Architecture

The system follows a microservices architecture with clear separation of concerns:

1. **Data Ingestion Layer**: Handles data from multiple sources
2. **Processing Engine**: Core business logic for user merging
3. **Graph Database Layer**: Neo4j for storing relationships
4. **API Gateway**: REST and GraphQL APIs
5. **Pipeline Orchestration**: Coordinates the entire workflow

## Performance Considerations

- **Batch Processing**: Large datasets are processed in configurable batches
- **Database Indexing**: Proper Neo4j indexes for performance
- **Connection Pooling**: Efficient database connection management
- **Parallel Processing**: Configurable worker pools for concurrent operations

## Security

- **Data Validation**: Comprehensive input validation
- **Error Handling**: Graceful error handling without data exposure
- **Audit Logging**: Track all data operations (to be implemented)
- **Configuration Security**: Environment-based configuration management

## Monitoring

- **Structured Logging**: JSON-formatted logs for easy parsing
- **Performance Metrics**: Built-in performance monitoring
- **Health Checks**: Database and service health monitoring
- **Error Tracking**: Comprehensive error reporting

## Roadmap

### Phase 1: MVP (Completed)
- [x] Basic CSV data processing
- [x] Email-based user merging
- [x] Neo4j graph database integration
- [x] Basic pipeline orchestration

### Phase 2: Production Features
- [ ] Database connectors for production systems
- [ ] Advanced merging algorithms
- [ ] Real-time data synchronization
- [ ] Enhanced error handling and monitoring

### Phase 3: Advanced Features
- [ ] GraphQL API implementation
- [ ] Advanced analytics and reporting
- [ ] User segmentation capabilities
- [ ] Comprehensive API documentation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `docs/` folder
- Review the design documents in the `design/` folder 