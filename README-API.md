# One ID Mapping System API

This document describes the REST API for the One ID Mapping System, which provides programmatic access to all data processing operations through HTTP endpoints.

## Overview

The API wraps all data processing steps under a unified REST interface, allowing you to:

- Ingest data from CSV files
- Execute data processing pipelines
- Clean database data
- Monitor system status
- Configure data sources

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements-api.txt
```

### 2. Start the API Server

```bash
python api_server.py
```

The server will start on `http://localhost:8000`

### 3. Access the API Documentation

- **Interactive API docs**: http://localhost:8000/docs
- **Alternative docs**: http://localhost:8000/redoc

## API Endpoints

### Health Check

```http
GET /health
```

Check if the API service is healthy and connected to the database.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00",
  "version": "1.0.0",
  "database_connected": true
}
```

### Data Ingestion

```http
POST /ingest
```

Ingest data from CSV files into the Neo4j database.

**Request Body:**
```json
{
  "account_email_mapping_file": "processed_data/account_email_mapping.csv",
  "account_properties_file": "processed_data/account_properties.csv",
  "clean_before_ingestion": false
}
```

**Response:**
```json
{
  "success": true,
  "accounts_created": 1000,
  "emails_created": 800,
  "relationships_created": 1000,
  "message": "Successfully ingested 1000 accounts, 800 emails, and 1000 relationships",
  "timestamp": "2024-01-15T10:30:00"
}
```

### Pipeline Execution

```http
POST /pipeline
```

Execute the data processing pipeline for a configured source.

**Request Body:**
```json
{
  "source_id": "example_source",
  "enable_merging": true,
  "preview_only": false
}
```

**Response:**
```json
{
  "success": true,
  "source_id": "example_source",
  "accounts_processed": 1000,
  "users_created": 50,
  "message": "Pipeline completed successfully. 50 users created from 1000 accounts.",
  "timestamp": "2024-01-15T10:30:00"
}
```

### Data Cleaning

```http
POST /clean
```

Clean all data from the Neo4j database.

**Response:**
```json
{
  "success": true,
  "nodes_deleted": 1000,
  "relationships_deleted": 1000,
  "remaining_nodes": 0,
  "remaining_relationships": 0,
  "message": "Successfully cleaned 1000 nodes and 1000 relationships",
  "timestamp": "2024-01-15T10:30:00"
}
```

### Database Statistics

```http
GET /stats
```

Get current database statistics.

**Response:**
```json
{
  "account_count": 1000,
  "email_count": 800,
  "relationship_count": 1000,
  "sample_accounts": ["acc_001", "acc_002", "acc_003"],
  "timestamp": "2024-01-15T10:30:00"
}
```

### Pipeline Status

```http
GET /status
```

Get the current status of the pipeline and configured sources.

**Response:**
```json
{
  "status": "ready",
  "database_connected": true,
  "configured_sources": 2,
  "sources": ["source1", "source2"],
  "timestamp": "2024-01-15T10:30:00"
}
```

### Source Configuration

```http
POST /configure-source?source_id=example&file_path=data.csv&columns=account_id,email
```

Configure a data source for processing.

**Response:**
```json
{
  "success": true,
  "message": "Source 'example' configured successfully",
  "file_path": "data.csv",
  "columns": ["account_id", "email"]
}
```

### List Sources

```http
GET /sources
```

List all configured data sources.

**Response:**
```json
{
  "sources": ["source1", "source2"],
  "count": 2
}
```

## CSV Processing Endpoints

### Process CSV

```http
POST /csv/process
```

Process a CSV file and return account data.

**Request Body:**
```json
{
  "file_path": "data/accounts.csv",
  "columns": ["account_id", "email", "reg_time"],
  "source_id": "my_source"
}
```

**Response:**
```json
{
  "success": true,
  "accounts_processed": 1000,
  "errors_count": 0,
  "errors": [],
  "message": "Successfully processed 1000 accounts from data/accounts.csv",
  "timestamp": "2024-01-15T10:30:00",
  "sample_accounts": [
    {
      "account_id": "acc_001",
      "email": "<EMAIL>",
      "deposit_amount_sum": 100.0,
      "reg_time": "2024-01-01 10:00:00"
    }
  ]
}
```

### Process Raw Data

```http
POST /csv/process-raw
```

Process all CSV files from the `raw_data` directory and save the combined results to `processed_data/processed_accounts.csv`.

**Request Body:**
```json
{
  "columns": ["account_id", "email", "deposit_amount_sum", "reg_time"],
  "chunk_size": 100000
}
```

**Response:**
```json
{
  "success": true,
  "files_processed": 3,
  "accounts_processed": 2500,
  "errors_count": 0,
  "errors": [],
  "output_files": {
    "account_email_mapping": "processed_data/account_email_mapping.csv",
    "account_properties": "processed_data/account_properties.csv"
  },
  "message": "Successfully processed 2500 accounts from 3 files. Output saved to processed_data/",
  "timestamp": "2024-01-15T10:30:00",
  "sample_accounts": [
    {
      "account_id": "46_148008110",
      "email": "<EMAIL>",
      "deposit_amount_sum": 100.0,
      "reg_time": "2024-01-01 10:00:00"
    }
  ]
}
```

**Notes:**
- Automatically processes all `*_results.csv` files in the `raw_data` directory
- Uses **chunked processing** for memory efficiency with large files
- **Project ID prefixing**: Creates unique account IDs (e.g., `46_148008110` from project 46)
- Outputs two separate CSV files:
  1. `account_email_mapping.csv` - contains account_id and email columns
  2. `account_properties.csv` - contains account_id and all other properties
- Creates the `processed_data` directory if it doesn't exist
- Uses default output filenames
- No file path input required - uses standard directory structure
- **chunk_size**: Controls memory usage (default: 100,000 rows per chunk)

### Validate CSV

```http
POST /csv/validate
```

Validate a CSV file structure and content.

**Request Body:**
```json
{
  "file_path": "data/accounts.csv",
  "columns": ["account_id", "email", "reg_time"]
}
```

**Response:**
```json
{
  "success": true,
  "is_valid": true,
  "total_rows": 1000,
  "valid_rows": 1000,
  "invalid_rows": 0,
  "validation_errors": [],
  "available_columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
  "message": "CSV validation passed. 1000 rows found.",
  "timestamp": "2024-01-15T10:30:00"
}
```

### Get CSV Info

```http
GET /csv/info?file_path=data/accounts.csv
```

Get detailed information about a CSV file.

**Response:**
```json
{
  "file_path": "data/accounts.csv",
  "file_size": 1024000,
  "total_rows": 1000,
  "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
  "sample_data": [
    {
      "account_id": "acc_001",
      "email": "<EMAIL>",
      "reg_time": "2024-01-01 10:00:00",
      "deposit_amount_sum": "100.0"
    }
  ],
  "timestamp": "2024-01-15T10:30:00"
}
```

### Get CSV Columns

```http
GET /csv/columns?file_path=data/accounts.csv
```

Get column names from a CSV file.

**Response:**
```json
{
  "file_path": "data/accounts.csv",
  "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
  "column_count": 4
}
```

## Usage Examples

### Python Client

```python
import asyncio
import aiohttp

async def main():
    async with aiohttp.ClientSession() as session:
        # Health check
        async with session.get("http://localhost:8000/health") as response:
            health = await response.json()
            print(f"API Status: {health['status']}")
        
        # Ingest data
        ingestion_data = {
            "account_email_mapping_file": "processed_data/account_email_mapping.csv",
            "account_properties_file": "processed_data/account_properties.csv",
            "clean_before_ingestion": True
        }
        
        async with session.post("http://localhost:8000/ingest", json=ingestion_data) as response:
            result = await response.json()
            print(f"Ingestion result: {result['message']}")

asyncio.run(main())
```

### cURL Examples

```bash
# Health check
curl -X GET "http://localhost:8000/health"

# Ingest data
curl -X POST "http://localhost:8000/ingest" \
  -H "Content-Type: application/json" \
  -d '{
    "account_email_mapping_file": "processed_data/account_email_mapping.csv",
    "account_properties_file": "processed_data/account_properties.csv",
    "clean_before_ingestion": true
  }'

# Execute pipeline
curl -X POST "http://localhost:8000/pipeline" \
  -H "Content-Type: application/json" \
  -d '{
    "source_id": "example_source",
    "enable_merging": true,
    "preview_only": false
  }'

# Get database stats
curl -X GET "http://localhost:8000/stats"

# Clean data
curl -X POST "http://localhost:8000/clean"

# CSV Processing Examples

# Get CSV info
curl -X GET "http://localhost:8000/csv/info?file_path=processed_data/account_properties.csv"

# Validate CSV
curl -X POST "http://localhost:8000/csv/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "processed_data/account_properties.csv",
    "columns": ["account_id", "reg_time", "deposit_amount_sum"]
  }'

# Process CSV
curl -X POST "http://localhost:8000/csv/process" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "processed_data/account_properties.csv",
    "columns": ["account_id", "reg_time", "deposit_amount_sum"],
    "source_id": "api_example"
  }'

# Process Raw Data
curl -X POST "http://localhost:8000/csv/process-raw" \
  -H "Content-Type: application/json" \
  -d '{
    "columns": ["account_id", "email", "deposit_amount_sum", "reg_time"],
    "chunk_size": 100000
  }'

# Get CSV columns
curl -X GET "http://localhost:8000/csv/columns?file_path=processed_data/account_properties.csv"

## Complete Workflow Example

Here's a typical workflow using the API:

1. **Start the server**:
   ```bash
   python api_server.py
   ```

2. **Check health**:
   ```bash
   curl http://localhost:8000/health
   ```

3. **Configure a data source**:
   ```bash
   curl -X POST "http://localhost:8000/configure-source?source_id=my_data&file_path=processed_data/account_properties.csv&columns=account_id,reg_time,deposit_amount_sum"
   ```

4. **Ingest data**:
   ```bash
   curl -X POST "http://localhost:8000/ingest" \
     -H "Content-Type: application/json" \
     -d '{
       "account_email_mapping_file": "account_email_mapping.csv",
       "account_properties_file": "account_properties.csv",
       "clean_before_ingestion": true
     }'
   ```

5. **Execute pipeline**:
   ```bash
   curl -X POST "http://localhost:8000/pipeline" \
     -H "Content-Type: application/json" \
     -d '{
       "source_id": "my_data",
       "enable_merging": true,
       "preview_only": false
     }'
   ```

6. **Check results**:
   ```bash
   curl http://localhost:8000/stats
   ```

## Error Handling

The API returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad request (invalid parameters)
- `500`: Internal server error
- `503`: Service unavailable (API not initialized)

Error responses include details:

```json
{
  "detail": "Data ingestion failed: File not found",
  "error": "File not found: processed_data/account_email_mapping.csv"
}
```

## Configuration

The API uses the same configuration as the main application:

- Database connection settings from environment variables
- Processing configuration with default file paths
- Logging configuration

## Development

### Running in Development Mode

```bash
python api_server.py
```

The server runs with auto-reload enabled for development.

### Testing

Use the provided client example:

```bash
python api_client_example.py
```

### API Documentation

The API includes automatic documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Architecture

The API is built with:

- **FastAPI**: Modern, fast web framework
- **Pydantic**: Data validation and serialization
- **Uvicorn**: ASGI server
- **aiohttp**: Async HTTP client (for examples)

The API gateway wraps all existing functionality:

- Data ingestion service
- User merging service
- Graph database operations
- Pipeline orchestration

## Security Considerations

For production deployment:

1. **Authentication**: Add authentication middleware
2. **CORS**: Configure CORS settings appropriately
3. **Rate Limiting**: Implement rate limiting
4. **HTTPS**: Use HTTPS in production
5. **Input Validation**: All inputs are validated by Pydantic models

## Monitoring

The API provides several monitoring endpoints:

- `/health`: Service health check
- `/status`: Pipeline and system status
- `/stats`: Database statistics

Use these endpoints for monitoring and alerting systems. 