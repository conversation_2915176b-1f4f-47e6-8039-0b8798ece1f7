#!/bin/bash

# Docker setup script for One ID Mapping System

set -e

echo "=== One ID Mapping System - Docker Setup ==="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p processed_data raw_data import design

# Copy example data if it exists
if [ -f "design/user_example_data.csv" ]; then
    echo "✅ Found example data"
else
    echo "⚠️  No example data found. Creating a sample CSV file..."
    cat > design/user_example_data.csv << EOF
account_id,email,deposit_amount_sum,deposit_times_sum,bet_money_sum,withdraw_amount_sum,current_money,reg_time,active_time,update_time,source_id,project_id
user001,<EMAIL>,1000.50,5,500.25,200.00,800.75,2024-01-01T10:00:00,2024-01-15T14:30:00,2024-01-20T09:15:00,source1,project1
user002,<EMAIL>,2500.00,8,1200.75,500.00,1800.25,2024-01-02T11:00:00,2024-01-16T15:45:00,2024-01-21T10:30:00,source1,project1
user003,<EMAIL>,800.25,3,300.50,100.00,700.75,2024-01-03T12:00:00,2024-01-17T16:20:00,2024-01-22T11:45:00,source2,project2
EOF
    echo "✅ Created sample CSV file"
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cat > .env << EOF
# Neo4j Configuration
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password123

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
BATCH_SIZE=1000
MERGE_CONFIDENCE_THRESHOLD=0.8

# File Paths
RAW_DATA_DIR=raw_data
PROCESSED_DATA_DIR=processed_data
IMPORT_DIR=import
EOF
    echo "✅ Created .env file"
fi

echo ""
echo "=== Building and Starting Services ==="

# Build and start services
echo "🐳 Building Docker images..."
docker-compose build

echo "🚀 Starting Neo4j..."
docker-compose up -d neo4j

echo "⏳ Waiting for Neo4j to be ready..."
sleep 30

echo "🔍 Checking Neo4j status..."
if docker-compose ps neo4j | grep -q "Up"; then
    echo "✅ Neo4j is running"
else
    echo "❌ Neo4j failed to start"
    docker-compose logs neo4j
    exit 1
fi

echo ""
echo "=== Setup Complete ==="
echo ""
echo "🎉 Your One ID Mapping System is ready!"
echo ""
echo "📊 Neo4j Browser: http://localhost:7474"
echo "   Username: neo4j"
echo "   Password: password123"
echo ""
echo "🚀 Available commands:"
echo "   docker-compose up app          # Run the main application"
echo "   docker-compose up test         # Run the test suite"
echo "   docker-compose down            # Stop all services"
echo "   docker-compose logs -f neo4j   # View Neo4j logs"
echo "   docker-compose logs -f app     # View application logs"
echo ""
echo "📁 Data directories:"
echo "   ./raw_data/                    # Raw CSV files"
echo "   ./processed_data/              # Processed CSV files"
echo "   ./import/                      # Neo4j import directory"
echo "   ./design/                      # Example and design files"
echo ""
echo "🔧 To add your own data:"
echo "   1. Place CSV files in ./raw_data/"
echo "   2. Run: docker-compose up app"
echo "" 