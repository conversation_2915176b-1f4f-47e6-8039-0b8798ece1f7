#!/usr/bin/env python3
"""
Test script to verify environment variables are loaded correctly in Docker.
"""

import os
import sys
from dotenv import load_dotenv

def test_env_loading():
    """Test environment variable loading."""
    print("🔍 Testing Environment Variable Loading")
    print("=" * 50)
    
    # Check if .env file exists
    env_file_exists = os.path.exists('.env')
    print(f"📁 .env file exists: {env_file_exists}")
    
    if env_file_exists:
        with open('.env', 'r') as f:
            content = f.read()
            print(f"📄 .env file content preview:")
            for i, line in enumerate(content.split('\n')[:5], 1):
                if line.strip() and not line.startswith('#'):
                    if 'PASSWORD' in line:
                        key, _ = line.split('=', 1)
                        print(f"   {i}: {key}=***")
                    else:
                        print(f"   {i}: {line}")
    
    print("\n🔧 Before load_dotenv():")
    print(f"   NEO4J_URI: {os.getenv('NEO4J_URI', 'Not set')}")
    print(f"   NEO4J_USERNAME: {os.getenv('NEO4J_USERNAME', 'Not set')}")
    print(f"   NEO4J_PASSWORD: {'***' if os.getenv('NEO4J_PASSWORD') else 'Not set'}")
    
    # Load environment variables
    load_dotenv()
    
    print("\n🔧 After load_dotenv():")
    print(f"   NEO4J_URI: {os.getenv('NEO4J_URI', 'Not set')}")
    print(f"   NEO4J_USERNAME: {os.getenv('NEO4J_USERNAME', 'Not set')}")
    print(f"   NEO4J_PASSWORD: {'***' if os.getenv('NEO4J_PASSWORD') else 'Not set'}")
    
    # Test pydantic settings
    print("\n⚙️  Testing pydantic settings:")
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        from src.config.settings import get_settings
        
        settings = get_settings()
        print(f"✅ Settings loaded successfully")
        print(f"   URI: {settings.neo4j_uri}")
        print(f"   Username: {settings.neo4j_username}")
        print(f"   Password: {'***' if settings.neo4j_password else 'Not set'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load settings: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_env_loading()
    if success:
        print("\n🎉 Environment loading test passed!")
    else:
        print("\n❌ Environment loading test failed!")
        sys.exit(1)
