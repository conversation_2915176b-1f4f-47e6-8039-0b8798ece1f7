#!/usr/bin/env python3
"""
Full pipeline demonstration with dynamic columns for the One ID Mapping System.
This script should be run as a module to avoid import issues.
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import the full pipeline components
from config.settings import get_settings
from config.database_config import Neo4jConfig
from config.processing_config import ProcessingConfig
from data_ingestion.ingestion_service import DataIngestionService
from user_merging.merging_service import UserMergingService
from graph_service.graph_data_ingest import GraphDataIngest
from data_pipeline.pipeline import DataProcessingPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_full_pipeline():
    """Run the complete pipeline with dynamic columns."""
    
    # Path to your raw data
    raw_data_path = "design/user_example_data.csv"
    
    print("=== Full Pipeline: Ingest, Merge, and Load to Neo4j ===")
    print(f"Processing data from: {raw_data_path}")
    print()
    
    try:
        # 1. Load settings and Neo4j config
        print("1. Loading configuration...")
        settings = get_settings()
        neo4j_config = Neo4jConfig(
            uri=settings.neo4j_uri,
            username=settings.neo4j_username,
            password=settings.neo4j_password
        )
        print("   ✓ Configuration loaded")
        
        # 2. Initialize graph manager and connect
        print("2. Connecting to Neo4j...")
        processing_config = ProcessingConfig()
        graph_manager = GraphDataIngest(neo4j_config, processing_config)
        await graph_manager.connect()
        print("   ✓ Connected to Neo4j")
        
        # 3. Initialize services
        print("3. Initializing services...")
        ingestion_service = DataIngestionService()
        merging_service = UserMergingService(
            graph_manager=graph_manager,
            confidence_threshold=settings.merge_confidence_threshold
        )
        pipeline = DataProcessingPipeline(
            ingestion_service=ingestion_service,
            merging_service=merging_service,
            graph_manager=graph_manager
        )
        print("   ✓ Services initialized")
        
        # 4. Configure CSV source with dynamic columns
        print("4. Configuring data source with dynamic columns...")
        dynamic_columns = ['account_id', 'email', 'active_time', 'reg_time', 'current_money', 'update_time']
        ingestion_service.configure_csv_source(
            source_id="dynamic_csv",
            file_path=raw_data_path,
            default_columns=dynamic_columns
        )
        print(f"   ✓ Configured CSV source with columns: {dynamic_columns}")
        
        # 5. Run the pipeline for this source
        print("5. Running data processing pipeline...")
        result = await pipeline.process_data_source("dynamic_csv")
        print(f"   ✓ Pipeline completed: {result}")
        
        # 6. Show some results
        print("6. Pipeline results:")
        print(f"   - Processed source: dynamic_csv")
        print(f"   - Columns used: {dynamic_columns}")
        print(f"   - Result: {result}")
        
    except Exception as e:
        logger.error(f"Pipeline error: {e}")
        print(f"   ✗ Pipeline error: {e}")
        raise
    finally:
        # 7. Cleanup
        print("7. Cleaning up...")
        if 'graph_manager' in locals():
            await graph_manager.disconnect()
            print("   ✓ Neo4j connection closed")
    
    print("\n=== Pipeline Complete ===")
    print("Data has been processed, merged, and loaded to Neo4j!")
    print("You can now query the graph database to see the results.")

if __name__ == "__main__":
    print("Running full pipeline...")
    asyncio.run(run_full_pipeline()) 