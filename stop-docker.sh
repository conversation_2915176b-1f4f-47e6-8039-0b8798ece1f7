#!/bin/bash

# One ID Mapping System - Docker Stopper
# This script stops and removes the Docker container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
CONTAINER_NAME="uniq_user_server"

print_info "🛑 One ID Mapping System - Docker Stop"
print_info "======================================"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running."
    exit 1
fi

# Check if container exists and is running
if docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_info "🛑 Stopping container..."
    docker stop ${CONTAINER_NAME}
    print_success "✅ Container stopped"
else
    print_warning "⚠️  Container is not running"
fi

# Check if container exists (stopped)
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_info "🗑️  Removing container..."
    docker rm ${CONTAINER_NAME}
    print_success "✅ Container removed"
else
    print_info "ℹ️  No container to remove"
fi

print_success "🎉 Cleanup complete!"
