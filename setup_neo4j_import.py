#!/usr/bin/env python3
"""
Setup script to configure Neo4j import directory for LOAD CSV functionality.
"""

import os
import shutil
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_neo4j_import_directory():
    """Setup Neo4j import directory for LOAD CSV access."""
    
    print("=== Neo4j Import Directory Setup ===")
    
    # Common Neo4j import directory locations
    possible_import_dirs = [
        "/var/lib/neo4j/import",  # Linux/Docker
        "/usr/local/var/lib/neo4j/import",  # macOS Homebrew
        "/opt/neo4j/import",  # Linux package
        "import",  # Relative to Neo4j home
        os.path.join(os.getcwd(), "import"),  # Current directory
    ]
    
    # Find existing import directory
    import_dir = None
    for dir_path in possible_import_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            import_dir = dir_path
            logger.info(f"Found existing Neo4j import directory: {import_dir}")
            break
    
    if import_dir is None:
        # Create import directory in current directory
        import_dir = os.path.join(os.getcwd(), "import")
        os.makedirs(import_dir, exist_ok=True)
        logger.info(f"Created Neo4j import directory: {import_dir}")
    
    # Test if we can write to the directory
    test_file = os.path.join(import_dir, "test.txt")
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        logger.info("✓ Import directory is writable")
    except Exception as e:
        logger.error(f"✗ Cannot write to import directory: {e}")
        return False
    
    # Copy example CSV file if it exists
    example_csv = "design/user_example_data.csv"
    if os.path.exists(example_csv):
        dest_csv = os.path.join(import_dir, "user_example_data.csv")
        shutil.copy2(example_csv, dest_csv)
        logger.info(f"✓ Copied example CSV to import directory: {dest_csv}")
    
    # Copy processed CSV file if it exists
    processed_csv = "processed_data/all_projects_combined.csv"
    if os.path.exists(processed_csv):
        dest_csv = os.path.join(import_dir, "all_projects_combined.csv")
        shutil.copy2(processed_csv, dest_csv)
        logger.info(f"✓ Copied processed CSV to import directory: {dest_csv}")
    
    print("\n=== Neo4j Configuration Instructions ===")
    print("To enable LOAD CSV functionality, you need to configure Neo4j:")
    print()
    print("1. Find your Neo4j configuration file (neo4j.conf):")
    print("   - Linux: /etc/neo4j/neo4j.conf")
    print("   - macOS: /usr/local/etc/neo4j/neo4j.conf")
    print("   - Windows: <neo4j-home>/conf/neo4j.conf")
    print()
    print("2. Add or modify the following line in neo4j.conf:")
    print(f"   dbms.directories.import={import_dir}")
    print()
    print("3. Restart Neo4j after making changes")
    print()
    print("4. Test the configuration by running:")
    print("   python test_batch_loading.py")
    print()
    print("Alternative: If you can't modify Neo4j config, the system will")
    print("automatically try to use the current directory as import directory.")
    
    return True

def test_csv_files():
    """Test if CSV files are accessible."""
    
    print("\n=== CSV File Test ===")
    
    csv_files = [
        "design/user_example_data.csv",
        "processed_data/all_projects_combined.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            logger.info(f"✓ Found CSV file: {csv_file}")
            
            # Check file size
            size = os.path.getsize(csv_file)
            logger.info(f"  File size: {size} bytes")
            
            # Check if file is readable
            try:
                with open(csv_file, 'r') as f:
                    first_line = f.readline().strip()
                    logger.info(f"  First line: {first_line[:50]}...")
            except Exception as e:
                logger.error(f"  ✗ Cannot read file: {e}")
        else:
            logger.warning(f"✗ CSV file not found: {csv_file}")

if __name__ == "__main__":
    setup_neo4j_import_directory()
    test_csv_files()
    
    print("\n=== Setup Complete ===")
    print("You can now try running the LOAD CSV test:")
    print("python test_batch_loading.py") 