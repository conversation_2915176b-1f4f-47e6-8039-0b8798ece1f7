# One ID Mapping System - Docker Setup

This document explains how to run the One ID Mapping System using Docker with Python 3.12 and Neo4j 2025.05.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

## Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd uniq_user_server

# Make the setup script executable
chmod +x docker-setup.sh

# Run the setup script
./docker-setup.sh
```

### 2. Manual Setup (Alternative)

If you prefer to set up manually:

```bash
# Create necessary directories
mkdir -p processed_data raw_data import design

# Create .env file
cat > .env << EOF
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password123
DEBUG=false
LOG_LEVEL=INFO
BATCH_SIZE=1000
MERGE_CONFIDENCE_THRESHOLD=0.8
RAW_DATA_DIR=raw_data
PROCESSED_DATA_DIR=processed_data
IMPORT_DIR=import
EOF

# Build and start services
docker-compose build
docker-compose up -d neo4j
```

## Services

The Docker Compose setup includes three services:

### 1. Neo4j Database (`neo4j`)
- **Image**: `neo4j:2025.05`
- **Ports**: 
  - 7474 (HTTP - Neo4j Browser)
  - 7687 (Bolt - Application connection)
- **Credentials**: neo4j/password123
- **Features**: APOC plugin enabled

### 2. Python Application (`app`)
- **Base**: Python 3.12-slim
- **Purpose**: Main data processing application
- **Dependencies**: Waits for Neo4j to be healthy

### 3. Test Service (`test`)
- **Base**: Python 3.12-slim
- **Purpose**: Run LOAD CSV tests
- **Dependencies**: Waits for Neo4j to be healthy

## Usage

### Start All Services

```bash
# Start Neo4j and application
docker-compose up app

# Or start in background
docker-compose up -d app
```

### Run Tests Only

```bash
# Run LOAD CSV tests
docker-compose up test
```

### Access Neo4j Browser

Open your browser and go to: http://localhost:7474
- **Username**: neo4j
- **Password**: password123

### View Logs

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f neo4j
docker-compose logs -f app
docker-compose logs -f test
```

### Stop Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete all data)
docker-compose down -v
```

## Data Management

### Directory Structure

```
uniq_user_server/
├── raw_data/           # Place your raw CSV files here
├── processed_data/     # Processed CSV files (auto-generated)
├── import/            # Neo4j import directory (auto-mounted)
├── design/            # Example and design files
└── docker-compose.yml
```

### Adding Your Data

1. **Place CSV files in `raw_data/` directory**
2. **Run the application**:
   ```bash
   docker-compose up app
   ```

### Example CSV Format

Your CSV files should have the following columns:
```csv
account_id,email,deposit_amount_sum,deposit_times_sum,bet_money_sum,withdraw_amount_sum,current_money,reg_time,active_time,update_time,source_id,project_id
user001,<EMAIL>,1000.50,5,500.25,200.00,800.75,2024-01-01T10:00:00,2024-01-15T14:30:00,2024-01-20T09:15:00,source1,project1
```

## Configuration

### Environment Variables

You can customize the setup by modifying the `.env` file:

```env
# Neo4j Configuration
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password123

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
BATCH_SIZE=1000
MERGE_CONFIDENCE_THRESHOLD=0.8

# File Paths
RAW_DATA_DIR=raw_data
PROCESSED_DATA_DIR=processed_data
IMPORT_DIR=import
```

### Neo4j Configuration

The Neo4j container is configured with:
- APOC plugin enabled
- Import directory mounted at `/import`
- Health checks enabled
- Persistent data storage

## Troubleshooting

### Common Issues

1. **Neo4j not starting**
   ```bash
   # Check Neo4j logs
   docker-compose logs neo4j
   
   # Check if ports are available
   netstat -tulpn | grep :7474
   ```

2. **Application can't connect to Neo4j**
   ```bash
   # Check if Neo4j is healthy
   docker-compose ps neo4j
   
   # Test connection manually
   docker-compose exec app python -c "
   from neo4j import GraphDatabase
   driver = GraphDatabase.driver('bolt://neo4j:7687', auth=('neo4j', 'password123'))
   print('Connection successful')
   driver.close()
   "
   ```

3. **LOAD CSV file access issues**
   ```bash
   # Check if files are in import directory
   docker-compose exec neo4j ls -la /import
   
   # Copy files manually if needed
   docker cp your_file.csv uniq_user_neo4j:/import/
   ```

### Reset Everything

```bash
# Stop all services and remove volumes
docker-compose down -v

# Remove all images
docker-compose down --rmi all

# Start fresh
./docker-setup.sh
```

## Development

### Building Custom Images

```bash
# Build with custom requirements
docker-compose build --no-cache

# Build specific service
docker-compose build app
```

### Running in Development Mode

```bash
# Start with debug mode
DEBUG=true docker-compose up app

# Or modify .env file
echo "DEBUG=true" >> .env
docker-compose up app
```

### Accessing Containers

```bash
# Access application container
docker-compose exec app bash

# Access Neo4j container
docker-compose exec neo4j bash

# Run Python commands
docker-compose exec app python main.py
```

## Performance

### Resource Allocation

The default setup uses minimal resources. For production:

```yaml
# Add to docker-compose.yml
services:
  neo4j:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
  app:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
```

### Scaling

```bash
# Scale application instances
docker-compose up --scale app=3
```

## Security

### Production Considerations

1. **Change default passwords**
2. **Use secrets management**
3. **Enable SSL/TLS**
4. **Restrict network access**
5. **Regular security updates**

### Example Production .env

```env
NEO4J_PASSWORD=your_secure_password_here
SECRET_KEY=your_secret_key_here
DEBUG=false
LOG_LEVEL=WARNING
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Docker and Neo4j logs
3. Ensure all prerequisites are met
4. Verify file permissions and paths 