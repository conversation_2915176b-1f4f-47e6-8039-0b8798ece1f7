# One ID Mapping System - Development Design Document

## Executive Summary

This document provides a detailed technical design for the One ID Mapping System, focusing on core modules, function-level design, and implementation architecture. The system is designed as a microservices-based platform with Neo4j as the primary graph database for user identity resolution.

## System Architecture Overview

The system follows a microservices architecture with the following key components:

1. **Data Ingestion Layer** - Handles data from multiple sources (CSV, databases, APIs)
2. **Processing Engine** - Core business logic for user merging and attribute aggregation
3. **Graph Database Layer** - Neo4j for storing user relationships and attributes
4. **API Gateway** - FastAPI-based REST and GraphQL APIs
5. **Web Dashboard** - React/Vue frontend for system monitoring and management

## Core Module Design

### 1. Data Ingestion Service

**Purpose**: Handles data ingestion from multiple sources and prepares data for processing.

**Key Functions**:

#### 1.1 CSV Data Processor
```python
class CSVDataProcessor:
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.validator = DataValidator()
    
    async def process_file(self, file_path: str) -> List[AccountData]:
        """Process CSV file and return validated account data"""
        # Read CSV file
        # Validate data format
        # Transform to AccountData objects
        # Return processed data
    
    def validate_account_data(self, data: AccountData) -> ValidationResult:
        """Validate individual account data record"""
        # Check required fields
        # Validate data types
        # Check business rules
        # Return validation result
```

#### 1.2 Database Connector
```python
class DatabaseConnector:
    def __init__(self, connection_config: DBConfig):
        self.config = connection_config
        self.connection_pool = None
    
    async def connect(self) -> bool:
        """Establish database connection"""
        # Create connection pool
        # Test connection
        # Return connection status
    
    async def fetch_accounts(self, query: str, params: dict) -> List[AccountData]:
        """Fetch account data from database"""
        # Execute query
        # Transform results
        # Return account data
```

### 2. User Merging Service

**Purpose**: Identifies and merges multiple accounts belonging to the same user.

**Key Functions**:

#### 2.1 Email-Based Merger
```python
class EmailBasedMerger:
    def __init__(self, config: MergingConfig):
        self.config = config
        self.confidence_threshold = config.confidence_threshold
    
    async def find_merge_candidates(self, accounts: List[AccountData]) -> List[MergeGroup]:
        """Find accounts that should be merged based on email"""
        # Group accounts by email
        # Calculate confidence scores
        # Filter by threshold
        # Return merge groups
    
    def calculate_merge_confidence(self, account1: AccountData, account2: AccountData) -> float:
        """Calculate confidence score for merging two accounts"""
        # Compare email addresses
        # Check additional attributes
        # Return confidence score (0.0 - 1.0)
```

#### 2.2 Merge Executor
```python
class MergeExecutor:
    def __init__(self, graph_service: GraphService):
        self.graph_service = graph_service
        self.audit_logger = AuditLogger()
    
    async def execute_merge(self, merge_group: MergeGroup) -> MergeResult:
        """Execute the merge operation"""
        # Create new user node
        # Link accounts to user
        # Update relationships
        # Log audit trail
        # Return merge result
```

### 3. Attribute Aggregation Service

**Purpose**: Intelligently combines user attributes from multiple accounts.

**Key Functions**:

#### 3.1 Aggregation Engine
```python
class AggregationEngine:
    def __init__(self, rules: List[AggregationRule]):
        self.rules = rules
        self.aggregators = {
            'sum': SumAggregator(),
            'max': MaxAggregator(),
            'min': MinAggregator(),
            'latest': LatestAggregator(),
            'earliest': EarliestAggregator()
        }
    
    async def aggregate_user_attributes(self, accounts: List[AccountData]) -> UserAttributes:
        """Aggregate attributes from multiple accounts"""
        # Apply aggregation rules
        # Calculate aggregated values
        # Handle edge cases
        # Return user attributes
```

#### 3.2 Field-Specific Aggregators
```python
class FinancialAggregator:
    """Handles financial metrics aggregation"""
    
    def aggregate_deposits(self, accounts: List[AccountData]) -> float:
        """Sum all deposit amounts"""
        return sum(acc.deposit_amount_sum or 0 for acc in accounts)
    
    def aggregate_bets(self, accounts: List[AccountData]) -> float:
        """Sum all bet amounts"""
        return sum(acc.bet_money_sum or 0 for acc in accounts)
    
    def aggregate_current_money(self, accounts: List[AccountData]) -> float:
        """Use latest current money value"""
        return max(acc.current_money or 0 for acc in accounts)

class TimeAggregator:
    """Handles time-based metrics aggregation"""
    
    def aggregate_registration_time(self, accounts: List[AccountData]) -> datetime:
        """Find earliest registration time"""
        return min(acc.reg_time for acc in accounts if acc.reg_time)
    
    def aggregate_last_activity(self, accounts: List[AccountData]) -> datetime:
        """Find latest activity time"""
        return max(acc.active_time for acc in accounts if acc.active_time)
```

### 4. Graph Service

**Purpose**: Manages Neo4j graph database operations and complex queries.

**Key Functions**:

#### 4.1 Graph Database Manager
```python
class GraphDatabaseManager:
    def __init__(self, neo4j_config: Neo4jConfig):
        self.driver = GraphDatabase.driver(
            neo4j_config.uri,
            auth=(neo4j_config.username, neo4j_config.password)
        )
        self.session_pool = self.driver.session_pool()
    
    async def create_user_node(self, user_data: UserData) -> str:
        """Create new user node in graph"""
        with self.session_pool.acquire() as session:
            result = session.run(
                """
                CREATE (u:User {
                    user_id: $user_id,
                    total_deposits: $total_deposits,
                    total_bets: $total_bets,
                    earliest_reg_time: $earliest_reg_time,
                    latest_activity: $latest_activity
                })
                RETURN u.user_id
                """,
                user_data.dict()
            )
            return result.single()["u.user_id"]
    
    async def link_account_to_user(self, user_id: str, account_id: str) -> bool:
        """Create relationship between user and account"""
        with self.session_pool.acquire() as session:
            session.run(
                """
                MATCH (u:User {user_id: $user_id})
                MATCH (a:Account {account_id: $account_id})
                CREATE (u)-[:HAS_ACCOUNT]->(a)
                """,
                user_id=user_id, account_id=account_id
            )
            return True
```

#### 4.2 Graph Query Service
```python
class GraphQueryService:
    def __init__(self, graph_manager: GraphDatabaseManager):
        self.graph_manager = graph_manager
    
    async def get_user_profile(self, user_id: str) -> UserProfile:
        """Get complete user profile with all accounts"""
        with self.graph_manager.session_pool.acquire() as session:
            result = session.run(
                """
                MATCH (u:User {user_id: $user_id})
                OPTIONAL MATCH (u)-[:HAS_ACCOUNT]->(a:Account)
                OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:Email)
                RETURN u, collect(a) as accounts, collect(e) as emails
                """,
                user_id=user_id
            )
            return self._parse_user_profile(result.single())
    
    async def find_user_by_account(self, account_id: str) -> Optional[str]:
        """Find user_id by account_id"""
        with self.graph_manager.session_pool.acquire() as session:
            result = session.run(
                """
                MATCH (u:User)-[:HAS_ACCOUNT]->(a:Account {account_id: $account_id})
                RETURN u.user_id
                """,
                account_id=account_id
            )
            record = result.single()
            return record["u.user_id"] if record else None
```

### 5. API Gateway Service

**Purpose**: Provides REST and GraphQL APIs for external access.

**Key Functions**:

#### 5.1 REST API Endpoints
```python
class UserAPI:
    def __init__(self, user_service: UserService):
        self.user_service = user_service
    
    @app.get("/api/v1/users/{user_id}")
    async def get_user_profile(self, user_id: str) -> UserProfileResponse:
        """Get user profile by user_id"""
        try:
            profile = await self.user_service.get_user_profile(user_id)
            return UserProfileResponse(
                success=True,
                data=profile,
                message="User profile retrieved successfully"
            )
        except UserNotFoundError:
            raise HTTPException(status_code=404, detail="User not found")
    
    @app.get("/api/v1/accounts/{account_id}/user")
    async def get_user_by_account(self, account_id: str) -> UserByAccountResponse:
        """Get user profile by account_id"""
        try:
            user_id = await self.user_service.find_user_by_account(account_id)
            if user_id:
                profile = await self.user_service.get_user_profile(user_id)
                return UserByAccountResponse(
                    success=True,
                    data=profile,
                    message="User found by account"
                )
            else:
                raise HTTPException(status_code=404, detail="User not found for account")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
```

### 6. Data Processing Pipeline

**Purpose**: Orchestrates the entire data processing workflow.

**Key Functions**:

#### 6.1 Pipeline Orchestrator
```python
class DataProcessingPipeline:
    def __init__(self, 
                 ingestion_service: DataIngestionService,
                 merging_service: UserMergingService,
                 aggregation_service: AttributeAggregationService,
                 graph_service: GraphService):
        self.ingestion_service = ingestion_service
        self.merging_service = merging_service
        self.aggregation_service = aggregation_service
        self.graph_service = graph_service
        self.audit_logger = AuditLogger()
    
    async def process_data_source(self, source_id: str) -> PipelineResult:
        """Execute complete data processing pipeline"""
        try:
            # Step 1: Ingest data
            accounts = await self.ingestion_service.process_source(source_id)
            
            # Step 2: Find merge candidates
            merge_groups = await self.merging_service.find_merge_candidates(accounts)
            
            # Step 3: Execute merges
            merge_results = []
            for group in merge_groups:
                result = await self.merging_service.execute_merge(group)
                merge_results.append(result)
            
            # Step 4: Aggregate attributes
            for user_id, user_accounts in merge_results:
                attributes = await self.aggregation_service.aggregate_user_attributes(user_accounts)
                await self.graph_service.update_user_attributes(user_id, attributes)
            
            # Step 5: Log audit trail
            await self.audit_logger.log_pipeline_execution(source_id, len(accounts), len(merge_results))
            
            return PipelineResult(
                success=True,
                accounts_processed=len(accounts),
                users_created=len(merge_results),
                message="Pipeline completed successfully"
            )
            
        except Exception as e:
            await self.audit_logger.log_pipeline_error(source_id, str(e))
            raise PipelineError(f"Pipeline failed: {str(e)}")
```

## Data Models

### Core Data Structures

```python
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any

@dataclass
class AccountData:
    account_id: str
    email: Optional[str]
    deposit_amount_sum: Optional[float]
    deposit_times_sum: Optional[int]
    bet_money_sum: Optional[float]
    withdraw_amount_sum: Optional[float]
    active_time: Optional[datetime]
    reg_time: Optional[datetime]
    current_money: Optional[float]
    update_time: Optional[datetime]
    source_id: str

@dataclass
class UserData:
    user_id: str
    total_deposits: float
    total_bets: float
    earliest_reg_time: datetime
    latest_activity: datetime
    account_count: int
    email_count: int

@dataclass
class MergeGroup:
    accounts: List[AccountData]
    confidence_score: float
    merge_criteria: str
    merge_id: str

@dataclass
class UserProfile:
    user_id: str
    attributes: UserData
    accounts: List[AccountData]
    emails: List[str]
    created_at: datetime
    updated_at: datetime
```

## Configuration Management

### System Configuration

```python
@dataclass
class SystemConfig:
    neo4j: Neo4jConfig
    postgresql: PostgreSQLConfig
    redis: RedisConfig
    api: APIConfig
    processing: ProcessingConfig

@dataclass
class ProcessingConfig:
    batch_size: int = 1000
    max_workers: int = 4
    retry_attempts: int = 3
    retry_delay: int = 5
    merge_confidence_threshold: float = 0.8

@dataclass
class AggregationRules:
    rules: Dict[str, AggregationRule] = field(default_factory=dict)
    
    def __post_init__(self):
        # Default aggregation rules
        self.rules = {
            'deposit_amount_sum': AggregationRule('sum', 'financial'),
            'bet_money_sum': AggregationRule('sum', 'financial'),
            'withdraw_amount_sum': AggregationRule('sum', 'financial'),
            'deposit_times_sum': AggregationRule('sum', 'count'),
            'reg_time': AggregationRule('min', 'time'),
            'active_time': AggregationRule('max', 'time'),
            'current_money': AggregationRule('latest', 'state'),
            'email': AggregationRule('collect', 'identity')
        }
```

## Implementation Priority

### Phase 1: MVP (8-10 weeks)

1. **Week 1-2**: Set up Neo4j database and basic schema
2. **Week 3-4**: Implement CSV data processor and validation
3. **Week 5-6**: Build email-based user merging algorithm
4. **Week 7-8**: Create basic attribute aggregation engine
5. **Week 9-10**: Develop simple REST API and basic dashboard

### Phase 2: Production Features (6-8 weeks)

1. Database connectors for production systems
2. Advanced merging algorithms
3. Real-time data synchronization
4. Enhanced error handling and monitoring

### Phase 3: Advanced Features (6-8 weeks)

1. GraphQL API implementation
2. Advanced analytics and reporting
3. User segmentation capabilities
4. Comprehensive API documentation

## Key Design Principles

1. **Modularity**: Each service has clear responsibilities and interfaces
2. **Scalability**: Horizontal scaling through microservices architecture
3. **Reliability**: Comprehensive error handling and monitoring
4. **Performance**: Caching, indexing, and optimization strategies
5. **Security**: Authentication, authorization, and data protection
6. **Maintainability**: Clear separation of concerns and documentation

## Conclusion

This development design provides a comprehensive foundation for building the One ID Mapping System. The modular architecture ensures scalability, maintainability, and extensibility. Each core module has clear responsibilities and well-defined interfaces, enabling parallel development and easy testing.

The design prioritizes getting to a working MVP quickly while maintaining the flexibility to add advanced features in subsequent phases. The focus on email-based merging for the MVP allows for rapid validation of the core concept before adding more complex matching algorithms. 