# Function-Level Design - One ID Mapping System
# Function-Level Design - One ID Mapping System

## Overview

This document provides detailed function-level design specifications for the One ID Mapping System, including specific implementation details, algorithms, and data flows for each core function.

## 1. Data Ingestion Functions

### 1.1 CSV Data Processing Functions

#### `process_csv_file(file_path: str, config: CSVConfig) -> List[AccountData]`

**Purpose**: Process CSV file and convert to standardized AccountData objects

**Input Parameters**:
- `file_path`: Path to CSV file
- `config`: CSV processing configuration

**Output**: List of validated AccountData objects

**Implementation Details**:
```python
async def process_csv_file(file_path: str, config: CSVConfig) -> List[AccountData]:
    accounts = []
    
    # Read CSV file
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
            try:
                # Transform row to AccountData
                account = transform_csv_row_to_account(row, config)
                
                # Validate account data
                validation_result = validate_account_data(account)
                if validation_result.is_valid:
                    accounts.append(account)
                else:
                    log_validation_error(row_num, validation_result.errors)
                    
            except Exception as e:
                log_processing_error(row_num, str(e))
                continue
    
    return accounts
```

#### `transform_csv_row_to_account(row: Dict, config: CSVConfig) -> AccountData`

**Purpose**: Transform CSV row to AccountData object

**Implementation Details**:
```python
def transform_csv_row_to_account(row: Dict, config: CSVConfig) -> AccountData:
    return AccountData(
        account_id=row[config.account_id_column],
        email=row.get(config.email_column),
        deposit_amount_sum=parse_float(row.get(config.deposit_amount_column)),
        deposit_times_sum=parse_int(row.get(config.deposit_times_column)),
        bet_money_sum=parse_float(row.get(config.bet_money_column)),
        withdraw_amount_sum=parse_float(row.get(config.withdraw_amount_column)),
        active_time=parse_datetime(row.get(config.active_time_column)),
        reg_time=parse_datetime(row.get(config.reg_time_column)),
        current_money=parse_float(row.get(config.current_money_column)),
        update_time=parse_datetime(row.get(config.update_time_column)),
        source_id=config.source_id
    )
```

### 1.2 Database Connection Functions

#### `connect_to_database(config: DBConfig) -> DatabaseConnection`

**Purpose**: Establish connection to source database

**Implementation Details**:
```python
async def connect_to_database(config: DBConfig) -> DatabaseConnection:
    try:
        if config.db_type == "postgresql":
            connection = await asyncpg.connect(
                host=config.host,
                port=config.port,
                user=config.username,
                password=config.password,
                database=config.database
            )
        elif config.db_type == "mysql":
            connection = await aiomysql.connect(
                host=config.host,
                port=config.port,
                user=config.username,
                password=config.password,
                db=config.database
            )
        else:
            raise ValueError(f"Unsupported database type: {config.db_type}")
        
        return DatabaseConnection(connection, config.db_type)
        
    except Exception as e:
        raise DatabaseConnectionError(f"Failed to connect to database: {str(e)}")
```

## 2. User Merging Functions

### 2.1 Email-Based Merging Functions

#### `find_email_merge_candidates(accounts: List[AccountData]) -> List[MergeGroup]`

**Purpose**: Find accounts that should be merged based on email addresses

**Algorithm**:
1. Group accounts by email address
2. Calculate confidence scores for each group
3. Filter groups by confidence threshold
4. Create merge groups

**Implementation Details**:
```python
async def find_email_merge_candidates(accounts: List[AccountData]) -> List[MergeGroup]:
    # Group accounts by email
    email_groups = {}
    for account in accounts:
        if account.email:
            email = account.email.lower().strip()
            if email not in email_groups:
                email_groups[email] = []
            email_groups[email].append(account)
    
    # Create merge groups for groups with multiple accounts
    merge_groups = []
    for email, account_list in email_groups.items():
        if len(account_list) > 1:
            confidence_score = calculate_email_merge_confidence(account_list)
            
            if confidence_score >= MERGE_CONFIDENCE_THRESHOLD:
                merge_group = MergeGroup(
                    accounts=account_list,
                    confidence_score=confidence_score,
                    merge_criteria="email",
                    merge_id=str(uuid.uuid4())
                )
                merge_groups.append(merge_group)
    
    return merge_groups
```

#### `calculate_email_merge_confidence(accounts: List[AccountData]) -> float`

**Purpose**: Calculate confidence score for email-based merging

**Confidence Factors**:
- Email address match (1.0 if exact match)
- Registration time proximity
- Activity pattern similarity
- Financial behavior similarity

**Implementation Details**:
```python
def calculate_email_merge_confidence(accounts: List[AccountData]) -> float:
    if len(accounts) < 2:
        return 0.0
    
    # Base confidence from email match
    base_confidence = 1.0
    
    # Time proximity factor
    time_confidence = calculate_time_proximity_confidence(accounts)
    
    # Activity pattern factor
    activity_confidence = calculate_activity_pattern_confidence(accounts)
    
    # Financial behavior factor
    financial_confidence = calculate_financial_behavior_confidence(accounts)
    
    # Weighted average of all factors
    total_confidence = (
        base_confidence * 0.4 +
        time_confidence * 0.2 +
        activity_confidence * 0.2 +
        financial_confidence * 0.2
    )
    
    return min(total_confidence, 1.0)
```

### 2.2 Merge Execution Functions

#### `execute_merge(merge_group: MergeGroup, graph_service: GraphService) -> MergeResult`

**Purpose**: Execute the merge operation in the graph database

**Implementation Details**:
```python
async def execute_merge(merge_group: MergeGroup, graph_service: GraphService) -> MergeResult:
    try:
        # Generate new user ID
        user_id = generate_user_id()
        
        # Create user node
        user_data = UserData(
            user_id=user_id,
            total_deposits=0.0,  # Will be calculated by aggregation
            total_bets=0.0,
            earliest_reg_time=None,
            latest_activity=None,
            account_count=len(merge_group.accounts),
            email_count=len(set(acc.email for acc in merge_group.accounts if acc.email))
        )
        
        await graph_service.create_user_node(user_data)
        
        # Link accounts to user
        for account in merge_group.accounts:
            await graph_service.create_account_node(account)
            await graph_service.link_account_to_user(user_id, account.account_id)
            
            # Create email node and link if email exists
            if account.email:
                await graph_service.create_email_node(account.email)
                await graph_service.link_account_to_email(account.account_id, account.email)
        
        # Log merge operation
        await log_merge_operation(merge_group, user_id)
        
        return MergeResult(
            success=True,
            user_id=user_id,
            accounts_merged=len(merge_group.accounts),
            merge_id=merge_group.merge_id
        )
        
    except Exception as e:
        # Rollback on error
        await rollback_merge_operation(merge_group.merge_id)
        raise MergeExecutionError(f"Failed to execute merge: {str(e)}")
```

## 3. Attribute Aggregation Functions

### 3.1 Core Aggregation Functions

#### `aggregate_user_attributes(accounts: List[AccountData], rules: AggregationRules) -> UserAttributes`

**Purpose**: Aggregate attributes from multiple accounts using defined rules

**Implementation Details**:
```python
async def aggregate_user_attributes(accounts: List[AccountData], rules: AggregationRules) -> UserAttributes:
    aggregated = {}
    
    # Get all unique field names from accounts
    all_fields = set()
    for account in accounts:
        all_fields.update(account.__dict__.keys())
    
    # Aggregate each field according to rules
    for field_name in all_fields:
        if field_name in ['account_id', 'source_id']:  # Skip non-aggregatable fields
            continue
            
        field_values = [getattr(acc, field_name) for acc in accounts if getattr(acc, field_name) is not None]
        
        if not field_values:
            continue
            
        rule = rules.get_rule(field_name)
        aggregated_value = apply_aggregation_rule(field_values, rule)
        aggregated[field_name] = aggregated_value
    
    return UserAttributes(**aggregated)
```

#### `apply_aggregation_rule(values: List, rule: AggregationRule) -> Any`

**Purpose**: Apply specific aggregation rule to list of values

**Implementation Details**:
```python
def apply_aggregation_rule(values: List, rule: AggregationRule) -> Any:
    if not values:
        return None
    
    if rule.aggregation_type == 'sum':
        return sum(values)
    
    elif rule.aggregation_type == 'max':
        return max(values)
    
    elif rule.aggregation_type == 'min':
        return min(values)
    
    elif rule.aggregation_type == 'latest':
        # For time-based fields, return the latest
        if all(isinstance(v, datetime) for v in values):
            return max(values)
        # For other fields, return the last non-None value
        return values[-1]
    
    elif rule.aggregation_type == 'earliest':
        # For time-based fields, return the earliest
        if all(isinstance(v, datetime) for v in values):
            return min(values)
        # For other fields, return the first non-None value
        return values[0]
    
    elif rule.aggregation_type == 'collect':
        # Return unique values
        return list(set(values))
    
    elif rule.aggregation_type == 'average':
        return sum(values) / len(values)
    
    else:
        raise ValueError(f"Unknown aggregation type: {rule.aggregation_type}")
```

### 3.2 Financial Aggregation Functions

#### `aggregate_financial_metrics(accounts: List[AccountData]) -> FinancialMetrics`

**Purpose**: Aggregate financial metrics with business logic

**Implementation Details**:
```python
def aggregate_financial_metrics(accounts: List[AccountData]) -> FinancialMetrics:
    total_deposits = sum(acc.deposit_amount_sum or 0 for acc in accounts)
    total_bets = sum(acc.bet_money_sum or 0 for acc in accounts)
    total_withdrawals = sum(acc.withdraw_amount_sum or 0 for acc in accounts)
    total_deposit_times = sum(acc.deposit_times_sum or 0 for acc in accounts)
    
    # Current money: use the latest value
    current_money_values = [acc.current_money for acc in accounts if acc.current_money is not None]
    current_money = max(current_money_values) if current_money_values else 0.0
    
    # Calculate derived metrics
    net_deposits = total_deposits - total_withdrawals
    average_deposit = total_deposits / total_deposit_times if total_deposit_times > 0 else 0.0
    
    return FinancialMetrics(
        total_deposits=total_deposits,
        total_bets=total_bets,
        total_withdrawals=total_withdrawals,
        total_deposit_times=total_deposit_times,
        current_money=current_money,
        net_deposits=net_deposits,
        average_deposit=average_deposit
    )
```

## 4. Graph Database Functions

### 4.1 Node Creation Functions

#### `create_user_node(user_data: UserData) -> str`

**Purpose**: Create user node in Neo4j graph database

**Implementation Details**:
```python
async def create_user_node(user_data: UserData) -> str:
    query = """
    CREATE (u:User {
        user_id: $user_id,
        total_deposits: $total_deposits,
        total_bets: $total_bets,
        earliest_reg_time: $earliest_reg_time,
        latest_activity: $latest_activity,
        account_count: $account_count,
        email_count: $email_count,
        created_at: datetime(),
        updated_at: datetime()
    })
    RETURN u.user_id
    """
    
    with self.session_pool.acquire() as session:
        result = session.run(query, user_data.dict())
        return result.single()["u.user_id"]
```

#### `create_account_node(account_data: AccountData) -> str`

**Purpose**: Create account node in Neo4j graph database

**Implementation Details**:
```python
async def create_account_node(account_data: AccountData) -> str:
    query = """
    CREATE (a:Account {
        account_id: $account_id,
        deposit_amount_sum: $deposit_amount_sum,
        deposit_times_sum: $deposit_times_sum,
        bet_money_sum: $bet_money_sum,
        withdraw_amount_sum: $withdraw_amount_sum,
        current_money: $current_money,
        reg_time: $reg_time,
        active_time: $active_time,
        update_time: $update_time,
        source_id: $source_id,
        created_at: datetime()
    })
    RETURN a.account_id
    """
    
    with self.session_pool.acquire() as session:
        result = session.run(query, account_data.dict())
        return result.single()["a.account_id"]
```

### 4.2 Relationship Functions

#### `link_account_to_user(user_id: str, account_id: str) -> bool`

**Purpose**: Create relationship between user and account

**Implementation Details**:
```python
async def link_account_to_user(user_id: str, account_id: str) -> bool:
    query = """
    MATCH (u:User {user_id: $user_id})
    MATCH (a:Account {account_id: $account_id})
    CREATE (u)-[:HAS_ACCOUNT]->(a)
    """
    
    with self.session_pool.acquire() as session:
        session.run(query, user_id=user_id, account_id=account_id)
        return True
```

### 4.3 Query Functions

#### `get_user_profile(user_id: str) -> UserProfile`

**Purpose**: Get complete user profile with all related data

**Implementation Details**:
```python
async def get_user_profile(user_id: str) -> UserProfile:
    query = """
    MATCH (u:User {user_id: $user_id})
    OPTIONAL MATCH (u)-[:HAS_ACCOUNT]->(a:Account)
    OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:Email)
    RETURN u, 
           collect(DISTINCT a) as accounts,
           collect(DISTINCT e.email_address) as emails
    """
    
    with self.session_pool.acquire() as session:
        result = session.run(query, user_id=user_id)
        record = result.single()
        
        if not record:
            raise UserNotFoundError(f"User {user_id} not found")
        
        user_node = record["u"]
        accounts = record["accounts"]
        emails = record["emails"]
        
        return UserProfile(
            user_id=user_node["user_id"],
            attributes=UserData(
                user_id=user_node["user_id"],
                total_deposits=user_node["total_deposits"],
                total_bets=user_node["total_bets"],
                earliest_reg_time=user_node["earliest_reg_time"],
                latest_activity=user_node["latest_activity"],
                account_count=user_node["account_count"],
                email_count=user_node["email_count"]
            ),
            accounts=[AccountData(**acc) for acc in accounts],
            emails=emails,
            created_at=user_node["created_at"],
            updated_at=user_node["updated_at"]
        )
```

## 5. API Functions

### 5.1 REST API Functions

#### `get_user_profile_api(user_id: str) -> UserProfileResponse`

**Purpose**: API endpoint to get user profile

**Implementation Details**:
```python
async def get_user_profile_api(user_id: str) -> UserProfileResponse:
    try:
        # Validate input
        if not user_id or not user_id.strip():
            raise HTTPException(status_code=400, detail="user_id is required")
        
        # Get user profile
        profile = await user_service.get_user_profile(user_id)
        
        return UserProfileResponse(
            success=True,
            data=profile,
            message="User profile retrieved successfully",
            timestamp=datetime.utcnow()
        )
        
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

#### `get_user_by_account_api(account_id: str) -> UserByAccountResponse`

**Purpose**: API endpoint to find user by account_id

**Implementation Details**:
```python
async def get_user_by_account_api(account_id: str) -> UserByAccountResponse:
    try:
        # Validate input
        if not account_id or not account_id.strip():
            raise HTTPException(status_code=400, detail="account_id is required")
        
        # Find user by account
        user_id = await user_service.find_user_by_account(account_id)
        
        if not user_id:
            raise HTTPException(status_code=404, detail="User not found for account")
        
        # Get user profile
        profile = await user_service.get_user_profile(user_id)
        
        return UserByAccountResponse(
            success=True,
            data=profile,
            message="User found by account",
            timestamp=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finding user by account: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

## 6. Utility Functions

### 6.1 Data Validation Functions

#### `is_valid_email(email: str) -> bool`

**Purpose**: Validate email format

**Implementation Details**:
```python
def is_valid_email(email: str) -> bool:
    if not email or not isinstance(email, str):
        return False
    
    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))
```

#### `parse_float(value: Any) -> Optional[float]`

**Purpose**: Safely parse float value

**Implementation Details**:
```python
def parse_float(value: Any) -> Optional[float]:
    if value is None or value == '':
        return None
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return None
```

#### `parse_datetime(value: Any) -> Optional[datetime]`

**Purpose**: Safely parse datetime value

**Implementation Details**:
```python
def parse_datetime(value: Any) -> Optional[datetime]:
    if value is None or value == '':
        return None
    
    if isinstance(value, datetime):
        return value
    
    try:
        # Try common datetime formats
        formats = [
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y-%m-%dT%H:%M:%SZ'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(str(value), fmt)
            except ValueError:
                continue
        
        # If no format matches, try parsing with dateutil
        from dateutil import parser
        return parser.parse(str(value))
        
    except Exception:
        return None
```

## Performance Considerations

### 1. Batch Processing

For large datasets, implement batch processing:

```python
async def process_accounts_in_batches(accounts: List[AccountData], batch_size: int = 1000):
    for i in range(0, len(accounts), batch_size):
        batch = accounts[i:i + batch_size]
        await process_account_batch(batch)
        await asyncio.sleep(0.1)  # Prevent overwhelming the system
```

### 2. Database Indexing

Create appropriate indexes for performance:

```cypher
// Neo4j indexes for performance
CREATE INDEX user_id_index FOR (u:User) ON (u.user_id);
CREATE INDEX account_id_index FOR (a:Account) ON (a.account_id);
CREATE INDEX email_index FOR (e:Email) ON (e.email_address);
CREATE INDEX user_account_relationship FOR ()-[r:HAS_ACCOUNT]-() ON (r);
```

### 3. Caching Strategy

Implement caching for frequently accessed data:

```python
async def get_user_profile_with_cache(user_id: str) -> UserProfile:
    # Try cache first
    cached = await cache.get(f"user_profile:{user_id}")
    if cached:
        return UserProfile.parse_raw(cached)
    
    # Get from database
    profile = await graph_service.get_user_profile(user_id)
    
    # Cache for 1 hour
    await cache.setex(f"user_profile:{user_id}", 3600, profile.json())
    
    return profile
```

## Error Handling

### 1. Graceful Degradation

```python
async def safe_aggregate_attributes(accounts: List[AccountData]) -> UserAttributes:
    try:
        return await aggregate_user_attributes(accounts)
    except Exception as e:
        logger.error(f"Aggregation failed: {str(e)}")
        # Return partial results or default values
        return UserAttributes(
            total_deposits=sum(acc.deposit_amount_sum or 0 for acc in accounts),
            total_bets=sum(acc.bet_money_sum or 0 for acc in accounts),
            # ... other fields with safe defaults
        )
```

### 2. Retry Logic

```python
async def retry_operation(operation, max_retries: int = 3, delay: float = 1.0):
    for attempt in range(max_retries):
        try:
            return await operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
```

This function-level design provides detailed implementation specifications for each core function in the One ID Mapping System, ensuring consistent and reliable operation across all components.