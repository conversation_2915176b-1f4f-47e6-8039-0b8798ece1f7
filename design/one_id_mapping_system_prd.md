# One ID Mapping System - Product Requirements Document

## Overview

The One ID Mapping System is a graph-based user identity resolution platform designed for gaming companies with multiple products. The system solves the critical problem of fragmented user identities across different gaming products by creating a unified user profile through intelligent account merging and attribute aggregation.

**Problem Statement**: Gaming companies operate multiple products, each generating separate account IDs for the same user. This creates data silos, prevents cross-product user insights, and complicates user experience and business analytics.

**Target Users**: 
- Data engineering teams managing user data across multiple gaming products
- Business analysts requiring unified user insights
- Product teams implementing cross-product features
- Compliance teams needing unified user verification

**Value Proposition**: 
- Single source of truth for user identity across all products
- Enhanced user experience through unified profiles
- Improved business intelligence and analytics
- Simplified compliance and KYC processes

## Core Features

### 1. Multi-Source Data Integration
**What it does**: Ingests user data from multiple gaming products and external systems (KYC, payment systems, etc.) through account_id as the primary key.

**Why it's important**: Establishes the foundation for comprehensive user data collection and enables cross-product data correlation.

**How it works**: 
- Connects to various data sources (databases, APIs, CSV files)
- Maps different user identifiers to account_id
- Validates and cleans incoming data
- Stores raw data in staging area for processing

### 2. Intelligent User Merging Engine
**What it does**: Automatically identifies and merges multiple accounts belonging to the same user based on configurable rules (currently email-based, extensible to other attributes).

**Why it's important**: Creates unified user profiles by resolving fragmented identities across products.

**How it works**:
- Applies email-based merging rules to group accounts
- Generates unique user_id for each merged user group
- Supports future expansion to multiple merging criteria (phone, payment accounts, device IDs)
- Implements community detection algorithms for complex merging scenarios

### 3. Attribute Aggregation & Calculation Engine
**What it does**: Intelligently combines user attributes from multiple accounts using configurable aggregation rules (sum, max, min, earliest, latest, etc.).

**Why it's important**: Provides accurate unified user profiles with meaningful aggregated metrics.

**How it works**:
- Applies field-specific aggregation rules:
  - Financial metrics (deposits, bets, withdrawals): SUM
  - Time-based metrics (registration, last activity): MIN/MAX
  - Count metrics (deposit times): SUM
  - Current state metrics (current money): LATEST
- Generates comprehensive user-level attributes
- Maintains audit trail of aggregation decisions

### 4. Graph Database Storage & Query Engine
**What it does**: Stores user relationships and attributes in Neo4j graph database for efficient querying and relationship traversal.

**Why it's important**: Enables complex relationship queries and provides flexible data model for future extensions.

**How it works**:
- Implements graph schema with User, Account, Email, and PaymentAccount nodes
- Establishes relationships: (User)-[:HAS_ACCOUNT]->(Account), (Account)-[:HAS_EMAIL]->(Email)
- Provides GraphQL/REST APIs for data access
- Supports complex graph queries for user discovery and analytics

### 5. Real-time Data Synchronization
**What it does**: Continuously updates user profiles as new data arrives from various sources.

**Why it's important**: Ensures user profiles remain current and accurate for real-time applications.

**How it works**:
- Implements change data capture (CDC) from source systems
- Processes incremental updates to existing user profiles
- Triggers re-merging when new accounts are discovered
- Maintains data consistency across all systems

### 6. API Gateway & Integration Layer
**What it does**: Provides standardized APIs for applications to access unified user data by account_id or user_id.

**Why it's important**: Enables seamless integration with existing systems and applications.

**How it works**:
- RESTful APIs for user profile retrieval
- GraphQL interface for complex queries
- Webhook notifications for profile updates
- Rate limiting and authentication

## User Experience

### User Personas

**Data Engineer (Primary)**
- Needs to configure data sources and merging rules
- Monitors data quality and processing pipelines
- Troubleshoots data integration issues
- Requires dashboard for system health monitoring

**Business Analyst**
- Queries unified user data for insights
- Generates reports on user behavior across products
- Analyzes user lifetime value and engagement patterns
- Needs self-service analytics tools

**Product Manager**
- Accesses user profiles for feature development
- Analyzes cross-product user journeys
- Makes data-driven product decisions
- Requires user segmentation capabilities

**Compliance Officer**
- Verifies user identity across products
- Monitors suspicious activity patterns
- Generates compliance reports
- Needs audit trail access

### Key User Flows

**Data Integration Flow**
1. Configure new data source connection
2. Map source fields to system schema
3. Validate data quality and format
4. Schedule data ingestion pipeline
5. Monitor processing status and errors

**User Merging Flow**
1. Review automatic merging suggestions
2. Approve or reject merge candidates
3. Configure merging rules and thresholds
4. Execute merging process
5. Validate merged user profiles

**Profile Access Flow**
1. Authenticate API request
2. Query user profile by account_id or user_id
3. Retrieve unified user attributes
4. Return formatted response
5. Log access for audit purposes

### UI/UX Considerations

**Dashboard Design**
- Clean, intuitive interface with clear navigation
- Real-time system health indicators
- Interactive data visualization for user insights
- Responsive design for mobile access

**Data Management Interface**
- Drag-and-drop data source configuration
- Visual rule builder for merging logic
- Bulk operations for data processing
- Search and filter capabilities

**API Documentation**
- Interactive API explorer
- Code examples in multiple languages
- Rate limit and authentication guides
- Error handling documentation

## Technical Architecture

### System Components

**Data Ingestion Layer**
- Apache Kafka for event streaming
- Apache Airflow for workflow orchestration
- Data validation and transformation services
- Error handling and retry mechanisms

**Processing Engine**
- Python-based data processing services
- Neo4j graph database for storage
- Redis for caching and session management
- Elasticsearch for full-text search

**API Layer**
- FastAPI for REST endpoints
- GraphQL server for complex queries
- API Gateway for routing and authentication
- Rate limiting and monitoring

**Storage Layer**
- Neo4j for graph data and relationships
- PostgreSQL for metadata and configuration
- S3/MinIO for file storage
- Backup and disaster recovery systems

### Data Models

**Graph Schema (Neo4j)**
```cypher
// Nodes
(User {user_id, total_deposits, total_bets, earliest_reg_time, latest_activity})
(Account {account_id, deposit_amount, bet_money, reg_time, current_money})
(Email {email_address})
(PaymentAccount {payment_id, payment_type})

// Relationships
(User)-[:HAS_ACCOUNT]->(Account)
(Account)-[:HAS_EMAIL]->(Email)
(Account)-[:HAS_PAYMENT_ACCOUNT]->(PaymentAccount)
```

**Relational Schema (PostgreSQL)**
- Configuration tables for data sources and rules
- Audit logs for data changes
- User session and authentication data
- System metadata and monitoring data

### APIs and Integrations

**Internal APIs**
- User Profile API: CRUD operations on user profiles
- Merging API: Execute and manage user merging
- Data Source API: Configure and monitor data sources
- Analytics API: Generate user insights and reports

**External Integrations**
- Gaming platform APIs for real-time data
- KYC service providers for identity verification
- Payment processors for transaction data
- Analytics platforms for data export

**Authentication & Security**
- OAuth 2.0 for API authentication
- Role-based access control (RBAC)
- Data encryption at rest and in transit
- Audit logging for compliance

### Infrastructure Requirements

**Compute Resources**
- Kubernetes cluster for container orchestration
- Auto-scaling based on load
- Multi-region deployment for global access
- Load balancing and failover

**Storage Requirements**
- High-performance SSD storage for Neo4j
- Scalable object storage for files
- Backup and archival systems
- Data retention policies

**Network & Security**
- VPC with private subnets
- API Gateway with DDoS protection
- SSL/TLS encryption
- Network monitoring and alerting

## Development Roadmap

### Phase 1: Foundation & Core Data Model (MVP)
**Duration**: 8-10 weeks

**Objectives**: Establish basic system architecture and core functionality

**Deliverables**:
- Neo4j database setup with basic schema
- Data ingestion pipeline for CSV files
- Email-based user merging algorithm
- Basic attribute aggregation (sum, max, min)
- Simple REST API for user profile access
- Basic web dashboard for system monitoring

**Key Features**:
- Import user data from CSV files
- Merge users based on email addresses
- Calculate basic aggregated attributes
- Query user profiles by account_id
- Basic error handling and logging

**Success Criteria**:
- Successfully process sample data from user_example_data.csv
- Generate unified user profiles with correct merging
- API responds within 500ms for profile queries
- System handles 1000+ user records without errors

### Phase 2: Production Data Integration & Advanced Merging
**Duration**: 6-8 weeks

**Objectives**: Enable production data sources and advanced merging capabilities

**Deliverables**:
- Database connectors for production systems
- Real-time data synchronization
- Advanced merging rules (phone, payment accounts)
- Community detection algorithms
- Enhanced attribute aggregation rules
- Data quality monitoring and validation

**Key Features**:
- Connect to multiple production databases
- Implement change data capture (CDC)
- Support multiple merging criteria
- Advanced aggregation algorithms
- Data quality dashboards
- Automated error detection and alerting

**Success Criteria**:
- Process data from 3+ production systems
- Merge users with 95%+ accuracy
- Real-time updates within 5 minutes
- Zero data loss during processing

### Phase 3: Advanced Analytics & API Ecosystem
**Duration**: 6-8 weeks

**Objectives**: Build comprehensive analytics and API ecosystem

**Deliverables**:
- GraphQL API for complex queries
- Advanced analytics and reporting
- User segmentation and targeting
- Cross-product user journey analysis
- API rate limiting and authentication
- Comprehensive documentation

**Key Features**:
- GraphQL interface for flexible queries
- User behavior analytics
- Cross-product insights
- Advanced reporting dashboards
- API authentication and authorization
- Developer portal and documentation

**Success Criteria**:
- Support complex graph queries
- Generate actionable user insights
- API handles 1000+ requests per minute
- 99.9% API uptime

### Phase 4: Enterprise Features & Scale
**Duration**: 8-10 weeks

**Objectives**: Enterprise-grade features and global scale

**Deliverables**:
- Multi-tenant architecture
- Advanced security and compliance
- Global data residency
- Machine learning for user matching
- Advanced monitoring and alerting
- Disaster recovery and backup

**Key Features**:
- Multi-tenant data isolation
- GDPR and privacy compliance
- Global data distribution
- ML-powered user matching
- Advanced monitoring and observability
- Automated disaster recovery

**Success Criteria**:
- Support 10+ gaming companies
- Process 1M+ user records
- 99.99% system uptime
- Full compliance with data regulations

## Logical Dependency Chain

### Foundation First Approach
1. **Database Schema & Basic Processing** (Phase 1)
   - Must establish Neo4j schema and basic data processing
   - Foundation for all subsequent features
   - Enables immediate value with sample data

2. **Production Data Integration** (Phase 2)
   - Builds on foundation to handle real data sources
   - Requires stable core processing engine
   - Enables production deployment

3. **Analytics & API Ecosystem** (Phase 3)
   - Requires comprehensive data model and processing
   - Builds user-facing features on stable backend
   - Enables business value realization

4. **Enterprise Scale** (Phase 4)
   - Requires mature system with proven reliability
   - Adds enterprise features to production-ready system
   - Enables global deployment and multi-tenant support

### Getting to Usable Frontend Quickly
- **Week 2-3**: Basic web dashboard for data monitoring
- **Week 4-5**: Simple user profile viewer
- **Week 6-8**: Data source configuration interface
- **Week 9-10**: Merging rule management UI

### Atomic Feature Development
Each feature is designed to be:
- **Independent**: Can be developed and tested separately
- **Incremental**: Builds upon previous features
- **Testable**: Has clear success criteria and validation
- **Deployable**: Can be released independently

## Risks and Mitigations

### Technical Challenges

**Risk**: Complex user merging logic may produce incorrect results
**Mitigation**: 
- Implement comprehensive testing with known datasets
- Build validation rules and confidence scoring
- Provide manual override capabilities for edge cases
- Regular accuracy audits and feedback loops

**Risk**: Neo4j performance degradation with large datasets
**Mitigation**:
- Implement proper indexing strategies
- Use Neo4j clustering for horizontal scaling
- Implement data partitioning and archiving
- Regular performance monitoring and optimization

**Risk**: Data synchronization issues across multiple sources
**Mitigation**:
- Implement robust error handling and retry mechanisms
- Use event sourcing for data lineage tracking
- Build data quality monitoring and alerting
- Implement rollback capabilities for failed updates

### MVP Scope Management

**Risk**: Feature creep extending development timeline
**Mitigation**:
- Strict prioritization based on business value
- Clear definition of MVP scope and success criteria
- Regular scope reviews and adjustments
- Phased delivery with incremental value

**Risk**: Integration complexity with existing systems
**Mitigation**:
- Start with simple CSV imports for validation
- Build adapters for each data source incrementally
- Comprehensive testing with production data samples
- Gradual migration strategy

### Resource Constraints

**Risk**: Limited expertise in graph databases and Neo4j
**Mitigation**:
- Invest in team training and certification
- Partner with Neo4j consultants for complex implementations
- Build knowledge sharing and documentation
- Hire specialized talent for critical components

**Risk**: Infrastructure costs for high-performance systems
**Mitigation**:
- Start with cloud-based solutions for flexibility
- Implement auto-scaling to optimize costs
- Regular cost monitoring and optimization
- Consider hybrid cloud strategies for cost efficiency

## Appendix

### Research Findings

**Graph Database Selection**
- Neo4j chosen for mature ecosystem and Cypher query language
- Alternative: Amazon Neptune for cloud-native deployment
- Decision based on team expertise and community support

**User Matching Algorithms**
- Email-based matching: 95%+ accuracy for gaming users
- Phone-based matching: 85%+ accuracy with international formats
- Payment account matching: 90%+ accuracy for financial data
- Combined approach: 98%+ accuracy with multiple criteria

**Performance Benchmarks**
- Neo4j can handle 1M+ user nodes with sub-second query times
- Graph queries scale linearly with proper indexing
- Memory requirements: 8GB+ for production workloads
- Storage: 10-20GB for 1M users with full attributes

### Technical Specifications

**System Requirements**
- Minimum 8GB RAM, 4 CPU cores
- SSD storage with 100GB+ capacity
- Network: 100Mbps+ for data ingestion
- Operating System: Linux (Ubuntu 20.04+)

**API Specifications**
- REST API: OpenAPI 3.0 specification
- GraphQL: Apollo Server implementation
- Authentication: JWT tokens with OAuth 2.0
- Rate Limiting: 1000 requests per minute per API key

**Data Formats**
- Input: CSV, JSON, XML, database connections
- Output: JSON, GraphQL responses, CSV exports
- Logging: Structured JSON logs with correlation IDs
- Monitoring: Prometheus metrics with Grafana dashboards

**Security Requirements**
- Data encryption: AES-256 at rest, TLS 1.3 in transit
- Authentication: Multi-factor authentication support
- Authorization: Role-based access control (RBAC)
- Audit: Comprehensive logging of all data access and changes 