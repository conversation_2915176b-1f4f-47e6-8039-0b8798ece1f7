# Core Modules and Function-Level Design - One ID Mapping System

## Executive Summary

Based on the PRD analysis, this document provides a detailed technical design focusing on core modules and function-level implementation for the One ID Mapping System. The system is designed as a microservices-based platform with Neo4j as the primary graph database for user identity resolution.

## System Architecture Overview

The system follows a microservices architecture with these key components:

1. **Data Ingestion Layer** - Handles data from multiple sources (CSV, databases, APIs)
2. **Processing Engine** - Core business logic for user merging and attribute aggregation
3. **Graph Database Layer** - Neo4j for storing user relationships and attributes
4. **API Gateway** - FastAPI-based REST and GraphQL APIs
5. **Web Dashboard** - React/Vue frontend for system monitoring and management

## Core Module Design

### 1. Data Ingestion Service

**Purpose**: Handles data ingestion from multiple sources and prepares data for processing.

**Key Functions**:

#### 1.1 CSV Data Processor
```python
class CSVDataProcessor:
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.validator = DataValidator()
    
    async def process_file(self, file_path: str) -> List[AccountData]:
        """Process CSV file and return validated account data"""
        accounts = []
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row_num, row in enumerate(reader, start=2):
                try:
                    account = self.transform_row_to_account(row)
                    if self.validator.validate_account(account):
                        accounts.append(account)
                except Exception as e:
                    self.logger.error(f"Error processing row {row_num}: {e}")
        return accounts
    
    def transform_row_to_account(self, row: Dict) -> AccountData:
        """Transform CSV row to AccountData object"""
        return AccountData(
            account_id=row['account_id'],
            email=row.get('email'),
            deposit_amount_sum=float(row.get('deposit_amount_sum', 0)),
            deposit_times_sum=int(row.get('deposit_times_sum', 0)),
            bet_money_sum=float(row.get('bet_money_sum', 0)),
            withdraw_amount_sum=float(row.get('withdraw_amount_sum', 0)),
            active_time=parse_datetime(row.get('active_time')),
            reg_time=parse_datetime(row.get('reg_time')),
            current_money=float(row.get('current_money', 0)),
            update_time=parse_datetime(row.get('update_time')),
            source_id=self.config.source_id
        )
```

#### 1.2 Database Connector
```python
class DatabaseConnector:
    def __init__(self, config: DBConfig):
        self.config = config
        self.connection_pool = None
    
    async def connect(self) -> bool:
        """Establish database connection"""
        try:
            if self.config.db_type == "postgresql":
                self.connection_pool = await asyncpg.create_pool(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database
                )
            return True
        except Exception as e:
            raise DatabaseConnectionError(f"Failed to connect: {e}")
    
    async def fetch_accounts(self, query: str, params: dict) -> List[AccountData]:
        """Fetch account data from database"""
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, **params)
            return [self.row_to_account(row) for row in rows]
```

### 2. User Merging Engine

**Purpose**: Identifies and merges multiple accounts belonging to the same user.

**Key Functions**:

#### 2.1 Email-Based Merger
```python
class EmailBasedMerger:
    def __init__(self, config: MergingConfig):
        self.config = config
        self.confidence_threshold = config.confidence_threshold
    
    async def find_merge_candidates(self, accounts: List[AccountData]) -> List[MergeGroup]:
        """Find accounts that should be merged based on email"""
        email_groups = self.group_accounts_by_email(accounts)
        merge_groups = []
        
        for email, account_list in email_groups.items():
            if len(account_list) > 1:
                confidence = self.calculate_confidence(account_list)
                if confidence >= self.confidence_threshold:
                    merge_groups.append(MergeGroup(
                        accounts=account_list,
                        confidence_score=confidence,
                        merge_criteria="email",
                        merge_id=str(uuid.uuid4())
                    ))
        return merge_groups
    
    def group_accounts_by_email(self, accounts: List[AccountData]) -> Dict[str, List[AccountData]]:
        """Group accounts by email address"""
        groups = {}
        for account in accounts:
            if account.email:
                email = account.email.lower().strip()
                if email not in groups:
                    groups[email] = []
                groups[email].append(account)
        return groups
    
    def calculate_confidence(self, accounts: List[AccountData]) -> float:
        """Calculate confidence score for merging"""
        if len(accounts) < 2:
            return 0.0
        
        # Base confidence from email match
        base_confidence = 1.0
        
        # Time proximity factor
        time_confidence = self.calculate_time_proximity(accounts)
        
        # Financial behavior similarity
        financial_confidence = self.calculate_financial_similarity(accounts)
        
        # Weighted average
        total_confidence = (
            base_confidence * 0.5 +
            time_confidence * 0.25 +
            financial_confidence * 0.25
        )
        
        return min(total_confidence, 1.0)
```

#### 2.2 Merge Executor
```python
class MergeExecutor:
    def __init__(self, graph_service: GraphService):
        self.graph_service = graph_service
        self.audit_logger = AuditLogger()
    
    async def execute_merge(self, merge_group: MergeGroup) -> MergeResult:
        """Execute the merge operation"""
        try:
            # Generate unique user ID
            user_id = self.generate_user_id()
            
            # Create user node
            user_data = UserData(
                user_id=user_id,
                account_count=len(merge_group.accounts),
                email_count=len(set(acc.email for acc in merge_group.accounts if acc.email))
            )
            
            await self.graph_service.create_user_node(user_data)
            
            # Link accounts to user
            for account in merge_group.accounts:
                await self.graph_service.create_account_node(account)
                await self.graph_service.link_account_to_user(user_id, account.account_id)
                
                if account.email:
                    await self.graph_service.create_email_node(account.email)
                    await self.graph_service.link_account_to_email(account.account_id, account.email)
            
            # Log merge operation
            await self.audit_logger.log_merge(merge_group, user_id)
            
            return MergeResult(
                success=True,
                user_id=user_id,
                accounts_merged=len(merge_group.accounts),
                merge_id=merge_group.merge_id
            )
            
        except Exception as e:
            await self.rollback_merge(merge_group.merge_id)
            raise MergeExecutionError(f"Merge failed: {e}")
```

### 3. Attribute Aggregation Engine

**Purpose**: Intelligently combines user attributes from multiple accounts.

**Key Functions**:

#### 3.1 Aggregation Engine
```python
class AggregationEngine:
    def __init__(self, rules: AggregationRules):
        self.rules = rules
        self.aggregators = {
            'sum': SumAggregator(),
            'max': MaxAggregator(),
            'min': MinAggregator(),
            'latest': LatestAggregator(),
            'earliest': EarliestAggregator()
        }
    
    async def aggregate_user_attributes(self, accounts: List[AccountData]) -> UserAttributes:
        """Aggregate attributes from multiple accounts"""
        aggregated = {}
        
        for field_name, rule in self.rules.get_rules().items():
            field_values = [getattr(acc, field_name) for acc in accounts if getattr(acc, field_name) is not None]
            
            if field_values:
                aggregator = self.aggregators.get(rule.aggregation_type)
                if aggregator:
                    aggregated[field_name] = aggregator.aggregate(field_values)
        
        return UserAttributes(**aggregated)
    
    def aggregate_financial_metrics(self, accounts: List[AccountData]) -> FinancialMetrics:
        """Aggregate financial metrics with business logic"""
        total_deposits = sum(acc.deposit_amount_sum or 0 for acc in accounts)
        total_bets = sum(acc.bet_money_sum or 0 for acc in accounts)
        total_withdrawals = sum(acc.withdraw_amount_sum or 0 for acc in accounts)
        total_deposit_times = sum(acc.deposit_times_sum or 0 for acc in accounts)
        
        # Current money: use the latest value
        current_money_values = [acc.current_money for acc in accounts if acc.current_money is not None]
        current_money = max(current_money_values) if current_money_values else 0.0
        
        # Calculate derived metrics
        net_deposits = total_deposits - total_withdrawals
        average_deposit = total_deposits / total_deposit_times if total_deposit_times > 0 else 0.0
        
        return FinancialMetrics(
            total_deposits=total_deposits,
            total_bets=total_bets,
            total_withdrawals=total_withdrawals,
            total_deposit_times=total_deposit_times,
            current_money=current_money,
            net_deposits=net_deposits,
            average_deposit=average_deposit
        )
```

#### 3.2 Aggregation Rules
```python
class AggregationRules:
    def __init__(self):
        self.rules = {
            'deposit_amount_sum': AggregationRule('sum', 'financial'),
            'deposit_times_sum': AggregationRule('sum', 'count'),
            'bet_money_sum': AggregationRule('sum', 'financial'),
            'withdraw_amount_sum': AggregationRule('sum', 'financial'),
            'active_time': AggregationRule('max', 'time'),
            'reg_time': AggregationRule('min', 'time'),
            'current_money': AggregationRule('latest', 'state'),
            'update_time': AggregationRule('max', 'time')
        }
    
    def get_rules(self) -> Dict[str, AggregationRule]:
        return self.rules
    
    def add_rule(self, field_name: str, rule: AggregationRule):
        self.rules[field_name] = rule
```

### 4. Graph Database Service

**Purpose**: Manages Neo4j graph database operations and queries.

**Key Functions**:

#### 4.1 Graph Database Manager
```python
class GraphDatabaseManager:
    def __init__(self, config: Neo4jConfig):
        self.driver = GraphDatabase.driver(
            config.uri,
            auth=(config.username, config.password)
        )
        self.session_pool = self.driver.session_pool()
    
    async def create_user_node(self, user_data: UserData) -> str:
        """Create user node in Neo4j"""
        query = """
        CREATE (u:User {
            user_id: $user_id,
            total_deposits: $total_deposits,
            total_bets: $total_bets,
            earliest_reg_time: $earliest_reg_time,
            latest_activity: $latest_activity,
            account_count: $account_count,
            email_count: $email_count,
            created_at: datetime(),
            updated_at: datetime()
        })
        RETURN u.user_id
        """
        
        with self.session_pool.acquire() as session:
            result = session.run(query, user_data.dict())
            return result.single()["u.user_id"]
    
    async def create_account_node(self, account_data: AccountData) -> str:
        """Create account node in Neo4j"""
        query = """
        CREATE (a:Account {
            account_id: $account_id,
            deposit_amount_sum: $deposit_amount_sum,
            deposit_times_sum: $deposit_times_sum,
            bet_money_sum: $bet_money_sum,
            withdraw_amount_sum: $withdraw_amount_sum,
            current_money: $current_money,
            reg_time: $reg_time,
            active_time: $active_time,
            update_time: $update_time,
            source_id: $source_id,
            created_at: datetime()
        })
        RETURN a.account_id
        """
        
        with self.session_pool.acquire() as session:
            result = session.run(query, account_data.dict())
            return result.single()["a.account_id"]
    
    async def link_account_to_user(self, user_id: str, account_id: str) -> bool:
        """Create relationship between user and account"""
        query = """
        MATCH (u:User {user_id: $user_id})
        MATCH (a:Account {account_id: $account_id})
        CREATE (u)-[:HAS_ACCOUNT]->(a)
        """
        
        with self.session_pool.acquire() as session:
            session.run(query, user_id=user_id, account_id=account_id)
            return True
```

#### 4.2 Graph Query Service
```python
class GraphQueryService:
    def __init__(self, graph_manager: GraphDatabaseManager):
        self.graph_manager = graph_manager
    
    async def get_user_profile(self, user_id: str) -> UserProfile:
        """Get complete user profile with all related data"""
        query = """
        MATCH (u:User {user_id: $user_id})
        OPTIONAL MATCH (u)-[:HAS_ACCOUNT]->(a:Account)
        OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:Email)
        RETURN u, 
               collect(DISTINCT a) as accounts,
               collect(DISTINCT e.email_address) as emails
        """
        
        with self.graph_manager.session_pool.acquire() as session:
            result = session.run(query, user_id=user_id)
            record = result.single()
            
            if not record:
                raise UserNotFoundError(f"User {user_id} not found")
            
            return self.parse_user_profile(record)
    
    async def find_user_by_account(self, account_id: str) -> Optional[str]:
        """Find user_id by account_id"""
        query = """
        MATCH (u:User)-[:HAS_ACCOUNT]->(a:Account {account_id: $account_id})
        RETURN u.user_id
        """
        
        with self.graph_manager.session_pool.acquire() as session:
            result = session.run(query, account_id=account_id)
            record = result.single()
            return record["u.user_id"] if record else None
    
    async def get_user_insights(self, user_id: str) -> UserInsights:
        """Get user insights and analytics"""
        query = """
        MATCH (u:User {user_id: $user_id})-[:HAS_ACCOUNT]->(a:Account)
        RETURN 
            count(a) as total_accounts,
            sum(a.deposit_amount_sum) as total_deposits,
            sum(a.bet_money_sum) as total_bets,
            min(a.reg_time) as earliest_registration,
            max(a.active_time) as latest_activity
        """
        
        with self.graph_manager.session_pool.acquire() as session:
            result = session.run(query, user_id=user_id)
            record = result.single()
            return UserInsights(**record)
```

### 5. API Gateway Service

**Purpose**: Provides REST and GraphQL APIs for data access.

**Key Functions**:

#### 5.1 REST API Endpoints
```python
class UserAPI:
    def __init__(self, user_service: UserService):
        self.user_service = user_service
    
    @app.get("/api/v1/users/{user_id}")
    async def get_user_profile(self, user_id: str) -> UserProfileResponse:
        """Get user profile by user_id"""
        try:
            profile = await self.user_service.get_user_profile(user_id)
            return UserProfileResponse(
                success=True,
                data=profile,
                message="User profile retrieved successfully"
            )
        except UserNotFoundError:
            raise HTTPException(status_code=404, detail="User not found")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/accounts/{account_id}/user")
    async def get_user_by_account(self, account_id: str) -> UserByAccountResponse:
        """Get user profile by account_id"""
        try:
            user_id = await self.user_service.find_user_by_account(account_id)
            if not user_id:
                raise HTTPException(status_code=404, detail="User not found for account")
            
            profile = await self.user_service.get_user_profile(user_id)
            return UserByAccountResponse(
                success=True,
                data=profile,
                message="User found by account"
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/merging/execute")
    async def execute_merging(self, request: MergingRequest) -> MergingResponse:
        """Execute user merging process"""
        try:
            result = await self.user_service.execute_merging(request.source_id)
            return MergingResponse(
                success=True,
                users_created=result.users_created,
                accounts_merged=result.accounts_merged,
                message="Merging completed successfully"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
```

### 6. Data Processing Pipeline

**Purpose**: Orchestrates the entire data processing workflow.

**Key Functions**:

#### 6.1 Pipeline Orchestrator
```python
class DataProcessingPipeline:
    def __init__(self, 
                 ingestion_service: DataIngestionService,
                 merging_service: UserMergingService,
                 aggregation_service: AttributeAggregationService,
                 graph_service: GraphService):
        self.ingestion_service = ingestion_service
        self.merging_service = merging_service
        self.aggregation_service = aggregation_service
        self.graph_service = graph_service
        self.audit_logger = AuditLogger()
    
    async def process_data_source(self, source_id: str) -> PipelineResult:
        """Execute complete data processing pipeline"""
        try:
            # Step 1: Ingest data
            accounts = await self.ingestion_service.process_source(source_id)
            
            # Step 2: Find merge candidates
            merge_groups = await self.merging_service.find_merge_candidates(accounts)
            
            # Step 3: Execute merges
            merge_results = []
            for group in merge_groups:
                result = await self.merging_service.execute_merge(group)
                merge_results.append(result)
            
            # Step 4: Aggregate attributes
            for result in merge_results:
                if result.success:
                    user_accounts = await self.graph_service.get_user_accounts(result.user_id)
                    attributes = await self.aggregation_service.aggregate_user_attributes(user_accounts)
                    await self.graph_service.update_user_attributes(result.user_id, attributes)
            
            # Step 5: Log pipeline execution
            await self.audit_logger.log_pipeline_execution(
                source_id, len(accounts), len(merge_results)
            )
            
            return PipelineResult(
                success=True,
                accounts_processed=len(accounts),
                users_created=len([r for r in merge_results if r.success]),
                message="Pipeline completed successfully"
            )
            
        except Exception as e:
            await self.audit_logger.log_pipeline_error(source_id, str(e))
            raise PipelineError(f"Pipeline failed: {e}")
```

## Data Models

### Core Data Structures

```python
@dataclass
class AccountData:
    account_id: str
    email: Optional[str]
    deposit_amount_sum: Optional[float]
    deposit_times_sum: Optional[int]
    bet_money_sum: Optional[float]
    withdraw_amount_sum: Optional[float]
    active_time: Optional[datetime]
    reg_time: Optional[datetime]
    current_money: Optional[float]
    update_time: Optional[datetime]
    source_id: str

@dataclass
class UserData:
    user_id: str
    total_deposits: float
    total_bets: float
    earliest_reg_time: datetime
    latest_activity: datetime
    account_count: int
    email_count: int

@dataclass
class MergeGroup:
    accounts: List[AccountData]
    confidence_score: float
    merge_criteria: str
    merge_id: str

@dataclass
class UserProfile:
    user_id: str
    attributes: UserData
    accounts: List[AccountData]
    emails: List[str]
    created_at: datetime
    updated_at: datetime

@dataclass
class FinancialMetrics:
    total_deposits: float
    total_bets: float
    total_withdrawals: float
    total_deposit_times: int
    current_money: float
    net_deposits: float
    average_deposit: float
```

## Implementation Priority

### Phase 1: MVP (8-10 weeks)

1. **Week 1-2**: Set up Neo4j database and basic schema
2. **Week 3-4**: Implement CSV data processor and validation
3. **Week 5-6**: Build email-based user merging algorithm
4. **Week 7-8**: Create basic attribute aggregation engine
5. **Week 9-10**: Develop simple REST API and basic dashboard

### Phase 2: Production Features (6-8 weeks)

1. Database connectors for production systems
2. Advanced merging algorithms
3. Real-time data synchronization
4. Enhanced error handling and monitoring

### Phase 3: Advanced Features (6-8 weeks)

1. GraphQL API implementation
2. Advanced analytics and reporting
3. User segmentation capabilities
4. Comprehensive API documentation

## Key Design Principles

1. **Modularity**: Each service has clear responsibilities and interfaces
2. **Scalability**: Horizontal scaling through microservices architecture
3. **Reliability**: Comprehensive error handling and monitoring
4. **Performance**: Caching, indexing, and optimization strategies
5. **Security**: Authentication, authorization, and data protection
6. **Maintainability**: Clear separation of concerns and documentation

## Performance Considerations

### 1. Batch Processing
```python
async def process_accounts_in_batches(accounts: List[AccountData], batch_size: int = 1000):
    for i in range(0, len(accounts), batch_size):
        batch = accounts[i:i + batch_size]
        await process_account_batch(batch)
        await asyncio.sleep(0.1)  # Prevent overwhelming the system
```

### 2. Database Indexing
```cypher
// Neo4j indexes for performance
CREATE INDEX user_id_index FOR (u:User) ON (u.user_id);
CREATE INDEX account_id_index FOR (a:Account) ON (a.account_id);
CREATE INDEX email_index FOR (e:Email) ON (e.email_address);
CREATE INDEX user_account_relationship FOR ()-[r:HAS_ACCOUNT]-() ON (r);
```

### 3. Caching Strategy
```python
async def get_user_profile_with_cache(user_id: str) -> UserProfile:
    # Try cache first
    cached = await cache.get(f"user_profile:{user_id}")
    if cached:
        return UserProfile.parse_raw(cached)
    
    # Get from database
    profile = await graph_service.get_user_profile(user_id)
    
    # Cache for 1 hour
    await cache.setex(f"user_profile:{user_id}", 3600, profile.json())
    
    return profile
```

## Error Handling

### 1. Graceful Degradation
```python
async def safe_aggregate_attributes(accounts: List[AccountData]) -> UserAttributes:
    try:
        return await aggregate_user_attributes(accounts)
    except Exception as e:
        logger.error(f"Aggregation failed: {str(e)}")
        # Return partial results or default values
        return UserAttributes(
            total_deposits=sum(acc.deposit_amount_sum or 0 for acc in accounts),
            total_bets=sum(acc.bet_money_sum or 0 for acc in accounts),
            # ... other fields with safe defaults
        )
```

### 2. Retry Logic
```python
async def retry_operation(operation, max_retries: int = 3, delay: float = 1.0):
    for attempt in range(max_retries):
        try:
            return await operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
```

## Conclusion

This development design provides a comprehensive foundation for building the One ID Mapping System. The modular architecture ensures scalability, maintainability, and extensibility. Each core module has clear responsibilities and well-defined interfaces, enabling parallel development and easy testing.

The design prioritizes getting to a working MVP quickly while maintaining the flexibility to add advanced features in subsequent phases. The focus on email-based merging for the MVP allows for rapid validation of the core concept before adding more complex matching algorithms.

The function-level design provides detailed implementation specifications for each core function, ensuring consistent and reliable operation across all components. The system is designed to handle the sample data from `user_example_data.csv` and can be extended to support production data sources and advanced features.
