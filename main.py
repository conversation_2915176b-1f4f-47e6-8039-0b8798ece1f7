"""
Main application entry point for the One ID Mapping System.
"""

import asyncio
import logging
from pathlib import Path
import os
import pandas as pd

from src.config.settings import get_settings
from src.config.database_config import Neo4jConfig
from src.config.processing_config import ProcessingConfig
from src.data_ingestion.ingestion_service import DataIngestionService
from src.user_merging.merging_service import UserMergingService
from src.graph_service.graph_data_ingest import GraphDataIngest
from src.data_pipeline.pipeline import DataProcessingPipeline
from src.models.account_data import AccountData
from src.utils.data_processor import (
    process_projects_with_dynamic_columns,
    get_project_summary,
    extract_user_profiles_from_projects,
    get_data_summary,
    validate_data_for_neo4j,
    get_processed_file_paths
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Main application function with complete pipeline from raw data to Neo4j."""
    try:
        # Load settings
        settings = get_settings()
        logger.info(f"Starting {settings.app_name} v{settings.app_version}")
        
        # Initialize Neo4j configuration
        neo4j_config = Neo4jConfig(
            uri=settings.neo4j_uri,
            username=settings.neo4j_username,
            password=settings.neo4j_password
        )
        
        # Initialize graph database manager with processing configuration
        processing_config = ProcessingConfig()
        graph_manager = GraphDataIngest(neo4j_config, processing_config)
        await graph_manager.connect()
        logger.info("✓ Connected to Neo4j")
        
        # Initialize services
        ingestion_service = DataIngestionService()
        merging_service = UserMergingService(
            graph_manager=graph_manager,
            confidence_threshold=settings.merge_confidence_threshold
        )
        
        # Initialize data processing pipeline
        pipeline = DataProcessingPipeline(
            ingestion_service=ingestion_service,
            merging_service=merging_service,
            graph_manager=graph_manager
        )
        
        # Step 1: Process raw data from multiple projects
        raw_data_dir = "raw_data"  # Directory containing project files
        processed_data_path = "processed_data/all_projects_combined.csv"
        
        logger.info("=== Step 1: Processing Raw Data from Multiple Projects ===")
        
        if os.path.exists(raw_data_dir):
            # Get project summary
            logger.info("Getting project summary...")
            summary = get_project_summary(raw_data_dir)
            logger.info(f"Found {summary['total_projects']} projects with {summary['total_rows']} total rows")
            
            for project_id, details in summary['project_details'].items():
                logger.info(f"  Project {project_id}: {details['rows']} rows, {details['columns']} columns")
            
            # Process all projects with dynamic column filtering
            logger.info("Processing all projects with dynamic column filtering...")
            dynamic_columns = ['account_id', 'email', 'reg_time', 'deposit_amount_sum']
            
            account_email_df, account_properties_df = process_projects_with_dynamic_columns(
                raw_data_dir=raw_data_dir,
                dynamic_columns=dynamic_columns,
                output_dir="processed_data"
            )
            
            logger.info(f"✓ Generated account-email mapping: {len(account_email_df)} records")
            logger.info(f"✓ Generated account properties: {len(account_properties_df)} records")
            
            # Use account_properties_df for further processing (contains all columns except email)
            df_combined = account_properties_df
            
            # Show sample account_ids
            if 'account_id' in df_combined.columns:
                sample_ids = df_combined['account_id'].head(3).tolist()
                logger.info(f"✓ Sample account_ids: {sample_ids}")
            
            # Validate combined data
            logger.info("Validating combined data...")
            issues = validate_data_for_neo4j(df_combined)
            if issues:
                logger.warning(f"Found {len(issues)} validation issues:")
                for issue in issues:
                    logger.warning(f"  - {issue}")
            else:
                logger.info("✓ Data validation passed")
            
            # Get data summary
            data_summary = get_data_summary(df_combined)
            logger.info(f"✓ Data summary: {data_summary['total_rows']} rows, {data_summary['total_columns']} columns")
            
            if 'project_distribution' in data_summary:
                logger.info(f"✓ Project distribution: {data_summary['project_distribution']}")
            
        else:
            logger.warning(f"Raw data directory {raw_data_dir} not found, using example data")
            # Fallback to example data
            csv_file_path = Path("design/user_example_data.csv")
            if csv_file_path.exists():
                processed_data_path = str(csv_file_path)
                logger.info(f"Using example data: {csv_file_path}")
            else:
                logger.error("No data source found!")
                return
        
        # Step 2: Configure ingestion service with processed data
        logger.info("=== Step 2: Configuring Data Ingestion ===")
        
        # Get file paths for processed data
        file_paths = get_processed_file_paths()
        account_properties_path = file_paths["account_properties"]
        account_email_mapping_path = file_paths["account_email_mapping"]
        
        # Configure CSV source with account properties data
        dynamic_columns = ['account_id', 'reg_time', 'deposit_amount_sum']  # email is now separate
        
        ingestion_service.configure_csv_source(
            source_id="multi_project_csv",
            file_path=account_properties_path,
            default_columns=dynamic_columns
        )
        logger.info(f"✓ Configured CSV source with account properties: {account_properties_path}")
        logger.info(f"✓ Using columns: {dynamic_columns}")
        
        # Step 3: Ingest and validate accounts
        logger.info("=== Step 3: Ingesting and Validating Data ===")
        
        accounts = await ingestion_service.process_source("multi_project_csv")
        logger.info(f"✓ Successfully ingested {len(accounts)} accounts")
        
        # Step 4: Load data to Neo4j using optimized ingestion
        logger.info("=== Step 4: Loading Data to Neo4j ===")
        
        try:
            # Use the new optimized ingestion method
            ingestion_result = await graph_manager.ingest_data_from_csv(
                account_email_mapping_file=account_email_mapping_path,
                account_properties_file=account_properties_path
            )
            
            logger.info(f"✓ Data ingestion completed successfully")
            logger.info(f"  - Accounts created: {ingestion_result['accounts_created']}")
            logger.info(f"  - Emails created: {ingestion_result['emails_created']}")
            logger.info(f"  - Relationships created: {ingestion_result['relationships_created']}")
            logger.info(f"  - Message: {ingestion_result['message']}")
            
        except Exception as e:
            logger.error(f"Error during data ingestion: {e}")
            raise
        
        # Step 5: Run merging pipeline (optional)
        logger.info("=== Step 5: Running User Merging Pipeline ===")
        
        try:
            result = await pipeline.process_data_source("multi_project_csv")
            logger.info(f"✓ Pipeline completed: {result}")
            
            # Show merging results
            if hasattr(result, 'accounts_processed'):
                logger.info(f"  - Accounts processed: {result.accounts_processed}")
            if hasattr(result, 'users_created'):
                logger.info(f"  - Users created: {result.users_created}")
            if hasattr(result, 'message'):
                logger.info(f"  - Message: {result.message}")
                
        except Exception as e:
            logger.warning(f"Pipeline execution failed: {e}")
        
        # Step 6: Query Neo4j to show results
        logger.info("=== Step 6: Querying Neo4j Results ===")
        
        try:
            await query_neo4j_results(graph_manager)
        except Exception as e:
            logger.error(f"Error querying Neo4j: {e}")
        
        # Cleanup
        await graph_manager.disconnect()
        logger.info("✓ Neo4j connection closed")
        
        logger.info("=== Pipeline Complete ===")
        logger.info("Data has been successfully processed and loaded to Neo4j!")
        logger.info("You can now query the graph database to see the results.")
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


async def query_neo4j_results(graph_manager):
    """Query Neo4j to show what data was loaded."""
    try:
        with graph_manager.driver.session() as session:
            # Count nodes
            result = session.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
            logger.info("Node counts in Neo4j:")
            for record in result:
                logger.info(f"  - {record['labels']}: {record['count']}")
            
            # Count relationships
            result = session.run("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count")
            logger.info("Relationship counts in Neo4j:")
            for record in result:
                logger.info(f"  - {record['type']}: {record['count']}")
            
            # Show some sample data
            result = session.run("MATCH (a:Account) RETURN a.account_id, a.email LIMIT 5")
            logger.info("Sample accounts in Neo4j:")
            for record in result:
                logger.info(f"  - {record['a.account_id']}: {record['a.email']}")
                
    except Exception as e:
        logger.error(f"Error querying Neo4j: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 