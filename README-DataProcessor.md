# Data Processor Refactoring

## Overview

The `data_processor.py` has been refactored to support dynamic column filtering and separate output files for better data organization.

## Key Changes

### 1. New Function: `process_projects_with_dynamic_columns()`

This function processes multiple project files from the `raw_data` directory and outputs two separate CSV files:

- **`account_email_mapping.csv`**: Contains `[account_id, email]` for mapping relationships
- **`account_properties.csv`**: Contains `[account_id, ...other_columns]` for account properties

### 2. Dynamic Column Filtering

You can now specify which columns to extract from the raw data using the `dynamic_columns` parameter:

```python
dynamic_columns = ['account_id', 'email', 'reg_time', 'deposit_amount_sum']
```

### 3. Project ID Generation

The function maintains the existing logic for generating unique project IDs by combining the project ID with the account ID (e.g., `46_148008110`).

## Usage

### Basic Usage

```python
from src.utils.data_processor import process_projects_with_dynamic_columns

# Process projects with specific columns
account_email_df, account_properties_df = process_projects_with_dynamic_columns(
    raw_data_dir="raw_data",
    dynamic_columns=['account_id', 'email', 'reg_time', 'deposit_amount_sum'],
    output_dir="processed_data"
)
```

### Getting File Paths

```python
from src.utils.data_processor import get_processed_file_paths

# Get the paths to generated files
file_paths = get_processed_file_paths()
account_email_path = file_paths["account_email_mapping"]
account_properties_path = file_paths["account_properties"]
```

## File Structure

After processing, the `processed_data` directory will contain:

```
processed_data/
├── account_email_mapping.csv    # [account_id, email]
└── account_properties.csv       # [account_id, reg_time, deposit_amount_sum, project_id, ...]
```

## Example Input/Output

### Input (Raw Data)
Raw data files in `raw_data/` directory:
- `46_results.csv`
- `47_results.csv`
- etc.

### Output Files

**account_email_mapping.csv:**
```csv
account_id,email
46_148008110,<EMAIL>
46_148008111,<EMAIL>
47_148008112,<EMAIL>
```

**account_properties.csv:**
```csv
account_id,reg_time,deposit_amount_sum,project_id
46_148008110,2023-01-01,1000.00,46
46_148008111,2023-01-02,2000.00,46
47_148008112,2023-01-03,1500.00,47
```

## Testing

Run the test script to verify the functionality:

```bash
python test_data_processor.py
```

## Integration with Main Application

The main application (`main.py`) has been updated to use the new functionality:

1. **Data Processing**: Uses `process_projects_with_dynamic_columns()` with dynamic columns
2. **Account Ingestion**: Uses `account_properties.csv` for account data
3. **Email Linking**: Uses `account_email_mapping.csv` for email relationships

## Benefits

1. **Separation of Concerns**: Email mapping and account properties are now separate
2. **Flexible Column Selection**: Can specify which columns to extract from raw data
3. **Better Data Organization**: Clear distinction between mapping data and property data
4. **Maintains Existing Logic**: Project ID generation and data cleaning remain unchanged
5. **Easy Integration**: Simple API for use in other parts of the application 