#!/bin/bash

# One ID Mapping System - Docker Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Function to show usage
show_usage() {
    echo "One ID Mapping System - Docker Startup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services (unified mode)"
    echo "  start-separate  Start services in separate containers"
    echo "  start-api       Start API service only"
    echo "  start-ui        Start UI service only"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  logs            Show logs for all services"
    echo "  logs-api        Show logs for API service"
    echo "  logs-ui         Show logs for UI service"
    echo "  status          Show status of all services"
    echo "  build           Build the Docker image"
    echo "  clean           Stop and remove all containers and volumes"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start        # Start unified services"
    echo "  $0 logs         # View logs"
    echo "  $0 stop         # Stop services"
}

# Function to start unified services
start_unified() {
    print_info "Starting unified services (API + UI in single container)..."
    docker-compose -f docker-compose-unified.yml up -d
    print_success "Services started successfully!"
    print_info "🌐 Streamlit UI: http://localhost:8501"
    print_info "🔗 FastAPI Server: http://localhost:8000"
    print_info "📚 API Documentation: http://localhost:8000/docs"
    print_info "🗄️ Neo4j Browser: http://localhost:7474"
}

# Function to start separate services
start_separate() {
    print_info "Starting separate services..."
    docker-compose up -d
    print_success "Services started successfully!"
    print_info "🌐 Streamlit UI: http://localhost:8501"
    print_info "🔗 FastAPI Server: http://localhost:8000"
    print_info "📚 API Documentation: http://localhost:8000/docs"
    print_info "🗄️ Neo4j Browser: http://localhost:7474"
}

# Function to start API only
start_api() {
    print_info "Starting API service only..."
    docker-compose -f docker-compose-unified.yml --profile api-only up -d
    print_success "API service started successfully!"
    print_info "🔗 FastAPI Server: http://localhost:8001"
    print_info "📚 API Documentation: http://localhost:8001/docs"
}

# Function to start UI only
start_ui() {
    print_info "Starting UI service only..."
    docker-compose -f docker-compose-unified.yml --profile ui-only up -d
    print_success "UI service started successfully!"
    print_info "🌐 Streamlit UI: http://localhost:8502"
}

# Function to stop services
stop_services() {
    print_info "Stopping all services..."
    docker-compose -f docker-compose-unified.yml down
    docker-compose down
    print_success "All services stopped"
}

# Function to restart services
restart_services() {
    print_info "Restarting services..."
    stop_services
    sleep 2
    start_unified
}

# Function to show logs
show_logs() {
    print_info "Showing logs for all services..."
    docker-compose -f docker-compose-unified.yml logs -f
}

# Function to show API logs
show_api_logs() {
    print_info "Showing API service logs..."
    docker-compose -f docker-compose-unified.yml logs -f app
}

# Function to show UI logs
show_ui_logs() {
    print_info "Showing UI service logs..."
    docker-compose -f docker-compose-unified.yml logs -f app
}

# Function to show status
show_status() {
    print_info "Service status:"
    docker-compose -f docker-compose-unified.yml ps
    echo ""
    print_info "Container resource usage:"
    docker stats --no-stream
}

# Function to build image
build_image() {
    print_info "Building Docker image..."
    docker-compose -f docker-compose-unified.yml build --no-cache
    print_success "Docker image built successfully"
}

# Function to clean up
clean_up() {
    print_warning "This will stop and remove all containers and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up..."
        docker-compose -f docker-compose-unified.yml down -v
        docker-compose down -v
        docker system prune -f
        print_success "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Main execution
main() {
    # Check prerequisites
    check_docker
    check_docker_compose
    
    # Parse command
    case "${1:-help}" in
        start)
            start_unified
            ;;
        start-separate)
            start_separate
            ;;
        start-api)
            start_api
            ;;
        start-ui)
            start_ui
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        logs-api)
            show_api_logs
            ;;
        logs-ui)
            show_ui_logs
            ;;
        status)
            show_status
            ;;
        build)
            build_image
            ;;
        clean)
            clean_up
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@" 