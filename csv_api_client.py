#!/usr/bin/env python3
"""
CSV Processing API Client
Simple client for testing the CSV processing REST API.
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional

class CSVAPIClient:
    """Client for the CSV Processing REST API."""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """Check if the API is healthy."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API Health: {data.get('status', 'Unknown')}")
                print(f"🔗 Database Connected: {data.get('database_connected', False)}")
                return True
            else:
                print(f"❌ API Health Check Failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    def get_csv_columns(self, file_path: str) -> List[str]:
        """Get columns from a CSV file."""
        try:
            print(f"🔍 Getting columns from: {file_path}")
            response = self.session.get(
                f"{self.base_url}/csv/columns",
                params={"file_path": file_path}
            )
            response.raise_for_status()
            result = response.json()
            columns = result.get("columns", [])
            print(f"📋 Available columns ({len(columns)}): {columns}")
            return columns
        except Exception as e:
            print(f"❌ Failed to get columns: {e}")
            return []
    
    def validate_csv(self, file_path: str, columns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Validate a CSV file."""
        try:
            print(f"🔍 Validating CSV file: {file_path}")
            data = {"file_path": file_path}
            if columns:
                data["columns"] = columns
            
            response = self.session.post(
                f"{self.base_url}/csv/validate",
                json=data
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("success"):
                print(f"✅ File is valid: {result['valid_rows']}/{result['total_rows']} rows")
                print(f"📊 Available columns: {result['available_columns']}")
            else:
                print(f"❌ File validation failed: {result.get('message', 'Unknown error')}")
            
            return result
        except Exception as e:
            print(f"❌ Validation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def process_raw_data(self, columns: Optional[List[str]] = None, chunk_size: int = 100000) -> Dict[str, Any]:
        """Process all CSV files in raw_data directory."""
        try:
            print("🔄 Starting raw data processing...")
            data = {"chunk_size": chunk_size}
            if columns:
                data["columns"] = columns
                print(f"📋 Using columns: {columns}")
            
            print(f"📦 Chunk size: {chunk_size:,}")
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/csv/process-raw",
                json=data
            )
            response.raise_for_status()
            result = response.json()
            end_time = time.time()
            
            if result.get("success"):
                print(f"✅ Processing completed in {end_time - start_time:.2f} seconds")
                print(f"📁 Files processed: {result['files_processed']}")
                print(f"👥 Accounts processed: {result['accounts_processed']:,}")
                print(f"📂 Output files:")
                for file_type, file_path in result['output_files'].items():
                    print(f"   - {file_type}: {file_path}")
                
                if result.get('errors'):
                    print(f"⚠️  Errors encountered: {len(result['errors'])}")
                    for error in result['errors'][:5]:  # Show first 5 errors
                        print(f"   - {error}")
            else:
                print(f"❌ Processing failed: {result.get('message', 'Unknown error')}")
            
            return result
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            return {"success": False, "error": str(e)}
    
    def process_single_file(self, file_path: str, columns: Optional[List[str]] = None, source_id: str = "") -> Dict[str, Any]:
        """Process a single CSV file."""
        try:
            print(f"🔄 Processing single file: {file_path}")
            data = {"file_path": file_path, "source_id": source_id}
            if columns:
                data["columns"] = columns
            
            response = self.session.post(
                f"{self.base_url}/csv/process",
                json=data
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("success"):
                print(f"✅ File processed successfully")
                print(f"👥 Accounts processed: {result['accounts_processed']:,}")
            else:
                print(f"❌ File processing failed: {result.get('message', 'Unknown error')}")
            
            return result
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_api_status(self) -> Dict[str, Any]:
        """Get API status information."""
        try:
            response = self.session.get(f"{self.base_url}/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Failed to get API status: {e}")
            return {}
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            response = self.session.get(f"{self.base_url}/stats")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Failed to get database stats: {e}")
            return {}


def main():
    """Main function to demonstrate CSV processing."""
    print("🚀 CSV Processing API Client")
    print("=" * 50)
    
    # Initialize client
    client = CSVAPIClient()
    
    # Check API health
    if not client.health_check():
        print("❌ API is not healthy. Please start the server first.")
        print("💡 Try: ./start-docker.sh start-api")
        return
    
    print("\n" + "=" * 50)
    
    # Get API status
    print("📊 API Status:")
    status = client.get_api_status()
    if status:
        print(f"   Status: {status.get('status', 'Unknown')}")
        print(f"   Database Connected: {status.get('database_connected', False)}")
        print(f"   Configured Sources: {status.get('configured_sources', 0)}")
    
    print("\n" + "=" * 50)
    
    # Example 1: Process raw data (recommended approach)
    print("🔄 EXAMPLE 1: Process Raw Data")
    print("-" * 30)
    
    # First, let's see what columns are available in a sample file
    sample_file = "raw_data/project_46.csv"  # Adjust this path as needed
    columns = client.get_csv_columns(sample_file)
    
    if columns:
        # Use common columns for processing
        common_columns = ["account_id", "email", "reg_time", "deposit_amount_sum"]
        available_columns = [col for col in common_columns if col in columns]
        
        if available_columns:
            print(f"\n📋 Using available columns: {available_columns}")
            result = client.process_raw_data(
                columns=available_columns,
                chunk_size=50000  # Smaller chunk size for testing
            )
        else:
            print("⚠️  No common columns found, processing with all columns")
            result = client.process_raw_data(chunk_size=50000)
    else:
        print("⚠️  Could not get columns, processing with default settings")
        result = client.process_raw_data(chunk_size=50000)
    
    print("\n" + "=" * 50)
    
    # Example 2: Validate a specific file
    print("🔍 EXAMPLE 2: Validate Specific File")
    print("-" * 30)
    
    validation = client.validate_csv(
        file_path=sample_file,
        columns=["account_id", "email"] if columns else None
    )
    
    print("\n" + "=" * 50)
    
    # Example 3: Get database stats
    print("📊 EXAMPLE 3: Database Statistics")
    print("-" * 30)
    
    stats = client.get_database_stats()
    if stats:
        print(f"   Account Count: {stats.get('account_count', 0):,}")
        print(f"   Email Count: {stats.get('email_count', 0):,}")
        print(f"   Relationship Count: {stats.get('relationship_count', 0):,}")
        if stats.get('sample_accounts'):
            print(f"   Sample Accounts: {stats['sample_accounts'][:3]}...")
    
    print("\n" + "=" * 50)
    print("✅ Demo completed!")
    print("\n💡 Next steps:")
    print("   1. Check the generated files in processed_data/")
    print("   2. Use the Streamlit UI to query the data")
    print("   3. Explore the API documentation at http://localhost:8008/docs")


if __name__ == "__main__":
    main() 