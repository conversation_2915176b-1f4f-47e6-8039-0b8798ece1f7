import os
import pandas as pd
from glob import glob

def process_raw_data(raw_dir, processed_dir, columns, chunk_size=100_000):
    os.makedirs(processed_dir, exist_ok=True)
    
    # Prepare output file paths
    mapping_path = os.path.join(processed_dir, 'account_email_mapping.csv')
    properties_path = os.path.join(processed_dir, 'account_properties.csv')
    
    # Collect all data first, then write once
    all_mapping_data = []
    all_properties_data = []
    
    # Get all csv files in raw_dir
    csv_files = glob(os.path.join(raw_dir, '*_results.csv'))
    
    print(f"Found {len(csv_files)} CSV files to process")

    for file_path in csv_files:
        # Extract project_id from filename
        project_id = os.path.basename(file_path).split('_')[0]
        print(f"Processing project {project_id} from {os.path.basename(file_path)}")
        
        # Read in chunks for large files
        for chunk in pd.read_csv(file_path, chunksize=chunk_size):
            # Filter columns
            chunk = chunk[columns]
            # Generate new account_id
            chunk['account_id'] = chunk['account_id'].apply(lambda x: f"{project_id}_{x}")
            
            # Prepare mapping and properties DataFrames
            mapping_df = chunk[['account_id', 'email']]
            properties_cols = [col for col in columns if col != 'email']
            properties_df = chunk[properties_cols]
            
            # Collect data instead of writing immediately
            all_mapping_data.append(mapping_df)
            all_properties_data.append(properties_df)

    # Write all data at once (overwrite mode with headers)
    if all_mapping_data:
        final_mapping_df = pd.concat(all_mapping_data, ignore_index=True)
        final_properties_df = pd.concat(all_properties_data, ignore_index=True)
        
        final_mapping_df.to_csv(mapping_path, index=False)
        final_properties_df.to_csv(properties_path, index=False)
        
        print(f"✓ Processing complete. Total accounts: {len(final_properties_df)}")
        print(f"✓ Output files:\n- {mapping_path}\n- {properties_path}")
    else:
        print("No data found to process")

# Define columns to filter
dynamic_columns = ['account_id', 'email', 'reg_time', 'deposit_amount_sum']

# Call the processing function
process_raw_data(
    raw_dir='raw_data',
    processed_dir='processed_data',
    columns=dynamic_columns
)