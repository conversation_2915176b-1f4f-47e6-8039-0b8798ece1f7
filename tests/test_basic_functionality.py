"""
Basic functionality tests for the One ID Mapping System.
"""

import pytest
from datetime import datetime

from src.models.account_data import AccountData
from src.models.user_data import UserData
from src.models.merge_data import MergeGroup, MergeResult
from src.data_ingestion.data_validator import DataValidator


class TestAccountData:
    """Test AccountData model functionality."""
    
    def test_account_data_creation(self):
        """Test creating AccountData with valid data."""
        account = AccountData(
            account_id="test123",
            email="<EMAIL>",
            deposit_amount_sum=100.0,
            bet_money_sum=50.0,
            source_id="test_source"
        )
        
        assert account.account_id == "test123"
        assert account.email == "<EMAIL>"
        assert account.deposit_amount_sum == 100.0
        assert account.bet_money_sum == 50.0
        assert account.source_id == "test_source"
    
    def test_account_data_validation(self):
        """Test AccountData validation."""
        # Test valid account
        account = AccountData(
            account_id="test123",
            email="<EMAIL>",
            deposit_amount_sum=100.0
        )
        # Should not raise any exceptions
        
        # Test invalid account (negative deposit)
        with pytest.raises(ValueError, match="deposit_amount_sum cannot be negative"):
            AccountData(
                account_id="test123",
                deposit_amount_sum=-100.0
            )
    
    def test_account_data_to_dict(self):
        """Test AccountData to_dict method."""
        account = AccountData(
            account_id="test123",
            email="<EMAIL>",
            deposit_amount_sum=100.0
        )
        
        data_dict = account.to_dict()
        assert data_dict["account_id"] == "test123"
        assert data_dict["email"] == "<EMAIL>"
        assert data_dict["deposit_amount_sum"] == 100.0


class TestUserData:
    """Test UserData model functionality."""
    
    def test_user_data_creation(self):
        """Test creating UserData with valid data."""
        user = UserData(
            user_id="user123",
            total_deposits=500.0,
            total_bets=200.0,
            account_count=3,
            email_count=2
        )
        
        assert user.user_id == "user123"
        assert user.total_deposits == 500.0
        assert user.total_bets == 200.0
        assert user.account_count == 3
        assert user.email_count == 2
    
    def test_user_data_validation(self):
        """Test UserData validation."""
        # Test valid user
        user = UserData(user_id="user123")
        # Should not raise any exceptions
        
        # Test invalid user (negative deposits)
        with pytest.raises(ValueError, match="total_deposits cannot be negative"):
            UserData(
                user_id="user123",
                total_deposits=-100.0
            )


class TestMergeGroup:
    """Test MergeGroup model functionality."""
    
    def test_merge_group_creation(self):
        """Test creating MergeGroup with valid data."""
        accounts = [
            AccountData(account_id="acc1", email="<EMAIL>"),
            AccountData(account_id="acc2", email="<EMAIL>")
        ]
        
        merge_group = MergeGroup(
            accounts=accounts,
            confidence_score=0.9,
            merge_criteria="email",
            merge_id="merge123"
        )
        
        assert len(merge_group.accounts) == 2
        assert merge_group.confidence_score == 0.9
        assert merge_group.merge_criteria == "email"
        assert merge_group.merge_id == "merge123"
    
    def test_merge_group_validation(self):
        """Test MergeGroup validation."""
        accounts = [
            AccountData(account_id="acc1", email="<EMAIL>"),
            AccountData(account_id="acc2", email="<EMAIL>")
        ]
        
        # Test valid merge group
        merge_group = MergeGroup(
            accounts=accounts,
            confidence_score=0.9,
            merge_criteria="email",
            merge_id="merge123"
        )
        # Should not raise any exceptions
        
        # Test invalid merge group (single account)
        with pytest.raises(ValueError, match="merge group must contain at least 2 accounts"):
            MergeGroup(
                accounts=[AccountData(account_id="acc1")],
                confidence_score=0.9,
                merge_criteria="email",
                merge_id="merge123"
            )
    
    def test_merge_group_methods(self):
        """Test MergeGroup utility methods."""
        accounts = [
            AccountData(account_id="acc1", email="<EMAIL>"),
            AccountData(account_id="acc2", email="<EMAIL>")
        ]
        
        merge_group = MergeGroup(
            accounts=accounts,
            confidence_score=0.9,
            merge_criteria="email",
            merge_id="merge123"
        )
        
        # Test get_account_ids
        account_ids = merge_group.get_account_ids()
        assert "acc1" in account_ids
        assert "acc2" in account_ids
        assert len(account_ids) == 2
        
        # Test get_emails
        emails = merge_group.get_emails()
        assert "<EMAIL>" in emails
        assert len(emails) == 1  # Should be unique


class TestDataValidator:
    """Test DataValidator functionality."""
    
    def test_validator_initialization(self):
        """Test DataValidator initialization."""
        validator = DataValidator()
        assert validator is not None
    
    def test_account_validation(self):
        """Test account validation."""
        validator = DataValidator()
        
        # Test valid account
        valid_account = AccountData(
            account_id="test123",
            email="<EMAIL>",
            deposit_amount_sum=100.0
        )
        assert validator.validate_account(valid_account) is True
        
        # Test invalid account (empty account_id)
        with pytest.raises(ValueError):
            invalid_account = AccountData(account_id="")
    
    def test_email_validation(self):
        """Test email validation."""
        validator = DataValidator()
        
        # Test valid emails
        assert validator._validate_email("<EMAIL>") is True
        assert validator._validate_email("<EMAIL>") is True
        
        # Test invalid emails
        assert validator._validate_email("invalid-email") is False
        assert validator._validate_email("") is False
        assert validator._validate_email("test@") is False


class TestMergeResult:
    """Test MergeResult model functionality."""
    
    def test_successful_merge_result(self):
        """Test creating successful MergeResult."""
        result = MergeResult(
            success=True,
            user_id="user123",
            accounts_merged=3,
            merge_id="merge123"
        )
        
        assert result.success is True
        assert result.user_id == "user123"
        assert result.accounts_merged == 3
        assert result.merge_id == "merge123"
    
    def test_failed_merge_result(self):
        """Test creating failed MergeResult."""
        result = MergeResult(
            success=False,
            merge_id="merge123",
            error_message="Merge failed due to validation error"
        )
        
        assert result.success is False
        assert result.merge_id == "merge123"
        assert result.error_message == "Merge failed due to validation error"
    
    def test_merge_result_validation(self):
        """Test MergeResult validation."""
        # Test successful result without user_id
        with pytest.raises(ValueError, match="user_id is required for successful merge"):
            MergeResult(
                success=True,
                accounts_merged=3,
                merge_id="merge123"
            )
        
        # Test failed result without error message
        with pytest.raises(ValueError, match="error_message is required for failed merge"):
            MergeResult(
                success=False,
                merge_id="merge123"
            )


if __name__ == "__main__":
    pytest.main([__file__]) 