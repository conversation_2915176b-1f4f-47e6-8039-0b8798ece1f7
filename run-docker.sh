#!/bin/bash

# One ID Mapping System - Docker Runner
# This script builds and runs the Docker container with both FastAPI and Streamlit services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
CONTAINER_NAME="uniq_user_server"
IMAGE_NAME="uniq_user_server:latest"
FASTAPI_PORT=8008
STREAMLIT_PORT=8018

# Get the current directory (project root)
PROJECT_ROOT=$(pwd)

print_info "🚀 One ID Mapping System - Docker Setup"
print_info "========================================"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "✅ Docker is running"

# Check if required directories exist
if [ ! -d "raw_data" ]; then
    print_warning "raw_data directory not found. Creating it..."
    mkdir -p raw_data
fi

if [ ! -d "processed_data" ]; then
    print_warning "processed_data directory not found. Creating it..."
    mkdir -p processed_data
fi

print_success "✅ Data directories ready"

# Stop and remove existing container if it exists
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_info "🛑 Stopping existing container..."
    docker stop ${CONTAINER_NAME} >/dev/null 2>&1 || true
    docker rm ${CONTAINER_NAME} >/dev/null 2>&1 || true
    print_success "✅ Existing container removed"
fi

# Build the Docker image
print_info "🔨 Building Docker image..."
if docker build -t ${IMAGE_NAME} .; then
    print_success "✅ Docker image built successfully"
else
    print_error "❌ Failed to build Docker image"
    exit 1
fi

# Run the container
print_info "🚀 Starting container..."
print_info "📊 FastAPI will be available at: http://localhost:${FASTAPI_PORT}"
print_info "🌐 Streamlit will be available at: http://localhost:${STREAMLIT_PORT}"
print_info "📁 Volume mounts:"
print_info "   • ${PROJECT_ROOT}/raw_data -> /app/raw_data"
print_info "   • ${PROJECT_ROOT}/processed_data -> /app/processed_data"

docker run -d \
    --name ${CONTAINER_NAME} \
    -p ${FASTAPI_PORT}:8008 \
    -p ${STREAMLIT_PORT}:8018 \
    -v "${PROJECT_ROOT}/raw_data:/app/raw_data" \
    -v "${PROJECT_ROOT}/processed_data:/app/processed_data" \
    -e PYTHONPATH=/app \
    ${IMAGE_NAME}

# Check if container started successfully
sleep 3
if docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_success "🎉 Container started successfully!"
    print_info ""
    print_info "📋 Service URLs:"
    print_info "   • FastAPI Server: http://localhost:${FASTAPI_PORT}"
    print_info "   • FastAPI Docs: http://localhost:${FASTAPI_PORT}/docs"
    print_info "   • Streamlit UI: http://localhost:${STREAMLIT_PORT}"
    print_info ""
    print_info "🔧 Useful commands:"
    print_info "   • View logs: docker logs ${CONTAINER_NAME}"
    print_info "   • Stop container: docker stop ${CONTAINER_NAME}"
    print_info "   • Remove container: docker rm ${CONTAINER_NAME}"
    print_info "   • Access container shell: docker exec -it ${CONTAINER_NAME} /bin/bash"
    print_info ""
    print_info "📊 Container status:"
    docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    print_error "❌ Container failed to start"
    print_info "📋 Container logs:"
    docker logs ${CONTAINER_NAME}
    exit 1
fi
