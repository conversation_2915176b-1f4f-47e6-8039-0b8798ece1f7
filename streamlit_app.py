#!/usr/bin/env python3
"""
Streamlit Web UI for Neo4j Query Interface
One ID Mapping System - Account Query Tool
"""

import streamlit as st
import pandas as pd
import asyncio
import logging
from typing import Optional, Dict, Any
import os
import sys

# Load environment variables from .env file
from dotenv import load_dotenv
# Try to load from current directory and parent directory
load_dotenv()
load_dotenv('.env')
load_dotenv('/app/.env')

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config.database_config import Neo4jConfig
from src.config.processing_config import ProcessingConfig
from src.graph_service.graph_data_ingest import GraphDataIngest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="One ID Mapping System - Account Query",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .data-section {
        background-color: #ffffff;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin: 1rem 0;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    .error-message {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

class Neo4jQueryInterface:
    """Interface for querying Neo4j database."""
    
    def __init__(self):
        self.graph_manager = None
        self.connected = False
    
    async def connect(self, uri: str, username: str, password: str) -> bool:
        """Connect to Neo4j database."""
        try:
            config = Neo4jConfig(
                uri=uri,
                username=username,
                password=password,
                max_connection_pool_size=10,
                connection_timeout=30,
                max_transaction_retry_time=15
            )
            
            processing_config = ProcessingConfig()
            self.graph_manager = GraphDataIngest(config, processing_config)
            await self.graph_manager.connect()
            
            # Test connection
            if await self.graph_manager.test_connection():
                self.connected = True
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Neo4j database."""
        if self.graph_manager:
            await self.graph_manager.disconnect()
            self.connected = False
    
    async def get_account_local_properties(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get local properties for a specific account."""
        if not self.connected or not self.graph_manager:
            return None
        
        try:
            with self.graph_manager.driver.session() as session:
                query = """
                MATCH (a:account {account_id: $account_id})
                RETURN a.account_id, a.reg_time, a.deposit_amount_sum,
                       a.deposit_times_sum, a.bet_money_sum, a.withdraw_amount_sum,
                       a.current_money, a.active_time, a.update_time,
                       a.favorate_machines
                """
                
                result = session.run(query, account_id=account_id)
                record = result.single()
                
                if record:
                    return {
                        'account_id': record.get('a.account_id'),
                        'reg_time': record.get('a.reg_time'),
                        'deposit_amount_sum': record.get('a.deposit_amount_sum'),
                        'deposit_times_sum': record.get('a.deposit_times_sum'),
                        'bet_money_sum': record.get('a.bet_money_sum'),
                        'withdraw_amount_sum': record.get('a.withdraw_amount_sum'),
                        'current_money': record.get('a.current_money'),
                        'active_time': record.get('a.active_time'),
                        'update_time': record.get('a.update_time'),
                        'favorate_machines': record.get('a.favorate_machines', [])
                    }
                return None
                
        except Exception as e:
            logger.error(f"Error getting account local properties: {e}")
            return None
    
    async def get_account_global_properties(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get global properties for a specific account (including related data)."""
        if not self.connected or not self.graph_manager:
            return None
        
        try:
            with self.graph_manager.driver.session() as session:
                # Get account with all related emails, deposit amounts, and global favorite machines
                query = """
                MATCH (a:account {account_id: $account_id})
                OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:email)
                WITH a, collect(DISTINCT e.email) as email_addresses
                OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:email)
                OPTIONAL MATCH (other_accounts:account)-[:HAS_EMAIL]->(e)
                WITH a, email_addresses, other_accounts,
                     // Collect all favorite machines from accounts sharing the same email
                     collect(CASE
                         WHEN other_accounts.favorate_machines IS NOT NULL
                         THEN other_accounts.favorate_machines
                         ELSE []
                     END) as all_favorite_machines_lists
                // Flatten and deduplicate favorite machines
                UNWIND all_favorite_machines_lists as machine_list
                UNWIND CASE
                    WHEN machine_list IS NOT NULL AND size(machine_list) > 0
                    THEN machine_list
                    ELSE [null]
                END as individual_machine
                WITH a, email_addresses, other_accounts,
                     collect(DISTINCT individual_machine) as global_favorate_machines_raw
                WITH a, email_addresses, other_accounts,
                     [machine IN global_favorate_machines_raw WHERE machine IS NOT NULL] as global_favorate_machines
                RETURN a.account_id, a.reg_time, a.deposit_amount_sum,
                       email_addresses as emails,
                       size(email_addresses) as email_count,
                       count(DISTINCT other_accounts) as total_accounts_same_email,
                       coalesce(sum(CASE
                           WHEN other_accounts.deposit_amount_sum IS NULL OR other_accounts.deposit_amount_sum = ''
                           THEN 0
                           ELSE toFloat(other_accounts.deposit_amount_sum)
                       END), 0) as total_deposit_amount_same_email,
                       min(CASE
                           WHEN other_accounts.reg_time IS NULL OR other_accounts.reg_time = ''
                           THEN '9999-12-31 23:59:59'
                           ELSE other_accounts.reg_time
                       END) as earliest_reg_time_same_email,
                       global_favorate_machines
                """
                
                result = session.run(query, account_id=account_id)
                record = result.single()
                
                if record:
                    return {
                        'account_id': record.get('a.account_id'),
                        'reg_time': record.get('a.reg_time'),
                        'deposit_amount_sum': record.get('a.deposit_amount_sum'),
                        'emails': record.get('emails', []) if record.get('emails') and record.get('emails') != [None] else [],
                        'email_count': record.get('email_count', 0),
                        'total_accounts_same_email': record.get('total_accounts_same_email', 0),
                        'total_deposit_amount_same_email': record.get('total_deposit_amount_same_email', 0) or 0,
                        'earliest_reg_time_same_email': record.get('earliest_reg_time_same_email'),
                        'global_favorate_machines': record.get('global_favorate_machines', [])
                    }
                return None
                
        except Exception as e:
            logger.error(f"Error getting account global properties: {e}")
            return None
    
    async def search_accounts_by_project(self, project_id: str) -> list[Dict[str, Any]]:
        """Search for accounts by project ID."""
        if not self.connected or not self.graph_manager:
            return []
        
        try:
            with self.graph_manager.driver.session() as session:
                query = """
                MATCH (a:account)
                WHERE a.account_id STARTS WITH $project_id + '_'
                RETURN a.account_id, a.reg_time, a.deposit_amount_sum,
                       a.deposit_times_sum, a.bet_money_sum, a.withdraw_amount_sum,
                       a.current_money
                ORDER BY a.account_id
                LIMIT 100
                """
                
                result = session.run(query, project_id=project_id)
                accounts = []
                
                for record in result:
                    accounts.append({
                        'account_id': record.get('a.account_id'),
                        'reg_time': record.get('a.reg_time'),
                        'deposit_amount_sum': record.get('a.deposit_amount_sum'),
                        'deposit_times_sum': record.get('a.deposit_times_sum'),
                        'bet_money_sum': record.get('a.bet_money_sum'),
                        'withdraw_amount_sum': record.get('a.withdraw_amount_sum'),
                        'current_money': record.get('a.current_money')
                    })
                
                return accounts
                
        except Exception as e:
            logger.error(f"Error searching accounts by project: {e}")
            return []
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        if not self.connected or not self.graph_manager:
            return {}
        
        try:
            with self.graph_manager.driver.session() as session:
                # Count nodes
                node_query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
                node_result = session.run(node_query)
                node_counts = {}
                for record in node_result:
                    # Convert list of labels to string for dictionary key
                    labels = record.get('labels')
                    if isinstance(labels, list):
                        label_key = ', '.join(labels) if labels else 'unlabeled'
                    else:
                        label_key = str(labels) if labels else 'unlabeled'
                    node_counts[label_key] = record.get('count', 0)
                
                # Count relationships
                rel_query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
                rel_result = session.run(rel_query)
                rel_counts = {record.get('type'): record.get('count', 0) for record in rel_result}
                
                return {
                    'node_counts': node_counts,
                    'relationship_counts': rel_counts
                }
                
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
    



def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🔍 用户信息库查询系统</h1>', unsafe_allow_html=True)
    st.markdown('<h2 style="text-align: center; color: #666;">Account Query Interface</h2>', unsafe_allow_html=True)
    
    # Initialize session state
    if 'neo4j_interface' not in st.session_state:
        st.session_state.neo4j_interface = Neo4jQueryInterface()
    
    # Load configuration for default values
    try:
        # Debug: Check environment variables
        logger.info(f"Environment variables check:")
        logger.info(f"NEO4J_URI: {os.getenv('NEO4J_URI', 'Not set')}")
        logger.info(f"NEO4J_USERNAME: {os.getenv('NEO4J_USERNAME', 'Not set')}")
        logger.info(f"NEO4J_PASSWORD: {'***' if os.getenv('NEO4J_PASSWORD') else 'Not set'}")

        from src.config.settings import get_settings
        settings = get_settings()
        default_uri = settings.neo4j_uri
        default_username = settings.neo4j_username
        default_password = settings.neo4j_password

        logger.info(f"Settings loaded successfully:")
        logger.info(f"URI: {default_uri}")
        logger.info(f"Username: {default_username}")
        logger.info(f"Password: {'***' if default_password else 'Not set'}")

    except Exception as e:
        logger.error(f"Could not load configuration: {e}")
        # Fallback to environment variables directly
        default_uri = os.getenv('NEO4J_URI', "bolt://localhost:7687")
        default_username = os.getenv('NEO4J_USERNAME', "neo4j")
        default_password = os.getenv('NEO4J_PASSWORD', "password")

        logger.info(f"Using fallback configuration:")
        logger.info(f"URI: {default_uri}")
        logger.info(f"Username: {default_username}")
        logger.info(f"Password: {'***' if default_password else 'Not set'}")
    
    # Sidebar for connection settings
    with st.sidebar:
        st.header("🔗 Database Connection")
        
        # Connection form
        with st.form("connection_form"):
            neo4j_uri = st.text_input("Neo4j URI", value=default_uri)
            neo4j_username = st.text_input("Username", value=default_username)
            
            # Password is read-only from configuration
            if default_password and default_password != "password":
                st.info("🔒 Password loaded from configuration")
                neo4j_password = default_password
            else:
                st.warning("⚠️ No password found in configuration")
                neo4j_password = ""
            
            connect_button = st.form_submit_button("Connect to Neo4j")
        
        if connect_button:
            with st.spinner("Connecting to Neo4j..."):
                # Run async connection
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    success = loop.run_until_complete(
                        st.session_state.neo4j_interface.connect(neo4j_uri, neo4j_username, neo4j_password)
                    )
                    if success:
                        st.success("✅ Connected to Neo4j!")
                    else:
                        st.error("❌ Connection failed!")
                finally:
                    loop.close()
        
        # Connection status
        if st.session_state.neo4j_interface.connected:
            st.success("🟢 Connected to Neo4j")
            st.info("✅ Ready to query account data")
        else:
            st.error("🔴 Disconnected")
            st.info("🔗 Please connect to Neo4j to start querying")
    
    # Main content area
    if st.session_state.neo4j_interface.connected:
        # Query interface
        st.header("🔍 用户信息查询")
        st.info("💡 Enter the project ID and original account ID to query account data")
        
        # Project ID dropdown
        project_options = [46, 59, 60, 96, 102, 105]
        selected_project_id = st.selectbox(
            "Select Project ID",
            options=project_options,
            format_func=lambda x: f"Project {x}",
            help="Choose the project ID from the dropdown"
        )
        
        # Original Account ID input
        original_account_id = st.text_input(
            "Original Account ID",
            placeholder="e.g., ********",
            help="Enter the original account ID (without project prefix)"
        )
        
        # Query button
        col1, col2 = st.columns([1, 1])
        with col1:
            query_button = st.button("🔍 Query Account", type="primary")
        
        with col2:
            # Initialize session state for data structure visibility
            if 'show_data_structure' not in st.session_state:
                st.session_state.show_data_structure = False
            
            # Toggle button
            if st.button("📋 Show Data Structure" if not st.session_state.show_data_structure else "📋 Hide Data Structure"):
                st.session_state.show_data_structure = not st.session_state.show_data_structure
            
            # Show data structure content if expanded
            if st.session_state.show_data_structure:
                st.subheader("📋 Expected Data Structure")
                st.markdown("""
                The system expects the following data structure in Neo4j:
                
                **Account Nodes** (`:account`):
                - `account_id`: Unique identifier (format: `project_id_original_account_id`)
                - `reg_time`: Registration timestamp
                - `deposit_amount_sum`: Total deposit amount
                - `deposit_times_sum`: Number of deposits
                - `bet_money_sum`: Total bet amount
                - `withdraw_amount_sum`: Total withdrawal amount
                - `current_money`: Current balance
                - `favorate_machines`: List of favorite machine names (e.g., ["tada_267", "winner_271"])
                
                **Email Nodes** (`:email`):
                - `email`: Email address
                
                **Relationships**:
                - `(account)-[:HAS_EMAIL]->(email)`
                
                **Supported Project IDs**:
                - 46, 59, 60, 96, 102, 105
                
                **Account ID Format**:
                - Original input: `project_id = "60"`, `original_account_id = "********"`
                - Combined in Neo4j: `account_id = "60_********"`
                - This format ensures unique account IDs across all projects

                **Global Features**:
                - `global_favorate_machines`: Deduplicated list of all favorite machines from accounts sharing the same email
                - Combines favorite machines using: `UNWIND + COLLECT(DISTINCT)` pattern
                - Handles accounts without favorite machines gracefully
                """)
        

        
        # Main query logic
        if query_button and original_account_id:
            # Combine project_id and original_account_id
            combined_account_id = f"{selected_project_id}_{original_account_id}"
            
            st.info(f"🔗 Querying for combined account ID: **{combined_account_id}**")
            
            with st.spinner("Querying account data..."):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    # Get local properties
                    local_props = loop.run_until_complete(
                        st.session_state.neo4j_interface.get_account_local_properties(combined_account_id)
                    )
                    
                    # Get global properties
                    global_props = loop.run_until_complete(
                        st.session_state.neo4j_interface.get_account_global_properties(combined_account_id)
                    )
                    
                    # Ensure global_props is a dictionary with safe defaults
                    if global_props is None:
                        global_props = {}
                    
                    if local_props or global_props:
                        # Display results
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.subheader("📋 Local Properties")
                            if local_props:
                                local_df = pd.DataFrame([local_props])
                                st.dataframe(local_df, use_container_width=True)

                                # Display favorite machines if available
                                favorite_machines = local_props.get('favorate_machines', [])
                                if favorite_machines:
                                    st.subheader("🎰 Favorite Machines")
                                    st.write(f"**Count:** {len(favorite_machines)}")
                                    st.write(f"**Machines:** {', '.join(favorite_machines)}")
                                else:
                                    st.info("ℹ️ No favorite machines recorded for this account")
                            else:
                                st.warning("No local properties found")
                        
                        with col2:
                            st.subheader("🌐 Global Properties")
                            if global_props:
                                # Create a more readable display for global properties
                                global_data = {
                                    'Account ID': global_props.get('account_id', 'N/A'),
                                    'Earliest Registration Time (Same Email)': global_props.get('earliest_reg_time_same_email', 'N/A'),
                                    'Total Deposit Amount (Same Email)': global_props.get('total_deposit_amount_same_email', 0),
                                    'Total Accounts (Same Email)': global_props.get('total_accounts_same_email', 0),
                                    'Emails': ', '.join(global_props.get('emails', [])) if global_props.get('emails') else 'None'
                                }
                                global_df = pd.DataFrame([global_data])
                                st.dataframe(global_df, use_container_width=True)
                                
                                # Display global favorite machines
                                global_favorite_machines = global_props.get('global_favorate_machines', [])
                                if global_favorite_machines:
                                    st.subheader("🌐 Global Favorite Machines")
                                    st.write(f"**Total Unique Machines:** {len(global_favorite_machines)}")
                                    st.write(f"**Combined Machines:** {', '.join(global_favorite_machines)}")
                                    st.info("ℹ️ These are all unique favorite machines from accounts sharing the same email")

                                # Add a note about what the new property means
                                total_accounts = global_props.get('total_accounts_same_email', 0)
                                total_deposit = global_props.get('total_deposit_amount_same_email', 0)

                                if total_accounts > 1:
                                    st.info(f"🔍 This email is associated with {total_accounts} total accounts (potential multi-account user)")
                                    st.info(f"💰 Total deposit amount across all accounts with same email: ${total_deposit:,.2f}")
                                elif total_accounts == 1:
                                    st.success("✅ This email is associated with only 1 account")
                                else:
                                    st.warning("⚠️ No email associated with this account")
                            else:
                                st.warning("No global properties found")
                    else:
                        st.error(f"❌ Account '{combined_account_id}' not found in database")
                        st.info("💡 Make sure the project_id and original_account_id combination exists in the database")
                finally:
                    loop.close()
        
        # Show database stats in main area
        st.markdown("---")
        st.subheader("📊 Database Overview")
        
        if st.button("🔄 Refresh Database Statistics"):
            with st.spinner("Loading database statistics..."):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    stats = loop.run_until_complete(
                        st.session_state.neo4j_interface.get_database_stats()
                    )
                    
                    if stats:
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.subheader("📈 Node Statistics")
                            for labels, count in stats.get('node_counts', {}).items():
                                st.metric(f"Label: {labels}", count)
                        
                        with col2:
                            st.subheader("🔗 Relationship Statistics")
                            for rel_type, count in stats.get('relationship_counts', {}).items():
                                st.metric(f"Type: {rel_type}", count)
                    else:
                        st.error("❌ Failed to load database statistics")
                finally:
                    loop.close()
    
    else:
        # Not connected state
        st.info("🔗 Please connect to Neo4j database using the sidebar to start querying.")
    
    # Footer
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #666;'>One ID Mapping System - Goodluck Studio </div>",
        unsafe_allow_html=True
    )


if __name__ == "__main__":
    main() 