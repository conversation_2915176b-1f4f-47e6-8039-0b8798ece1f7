# One ID Mapping System - Docker Setup

This document describes how to run the One ID Mapping System using Docker, with options for both separate services and unified deployment.

## 🏗️ Architecture

The system consists of three main components:

1. **Neo4j Database** - Graph database for storing user and account data
2. **FastAPI Server** - REST API for data processing and querying
3. **Streamlit UI** - Web interface for querying account data

## 🚀 Quick Start

### Option 1: Unified Deployment (Recommended)

Run both API and UI services in a single container:

```bash
# Start all services (Neo4j + API + UI)
docker-compose -f docker-compose-unified.yml up -d

# View logs
docker-compose -f docker-compose-unified.yml logs -f app

# Stop all services
docker-compose -f docker-compose-unified.yml down
```

**Access Points:**
- 🌐 **Streamlit UI**: http://localhost:8501
- 🔗 **FastAPI Server**: http://localhost:8000
- 📚 **API Documentation**: http://localhost:8000/docs
- 🗄️ **Neo4j Browser**: http://localhost:7474

### Option 2: Separate Services

Run API and UI as separate containers:

```bash
# Start all services
docker-compose up -d

# View logs for specific service
docker-compose logs -f api
docker-compose logs -f ui

# Stop all services
docker-compose down
```

### Option 3: Individual Services

Run only specific services:

```bash
# API only
docker-compose -f docker-compose-unified.yml --profile api-only up -d

# UI only
docker-compose -f docker-compose-unified.yml --profile ui-only up -d
```

## 📁 Project Structure

```
uniq_user_server/
├── docker-compose.yml              # Separate services setup
├── docker-compose-unified.yml      # Unified services setup
├── Dockerfile                      # Application container
├── docker-entrypoint.sh           # Container entrypoint script
├── start_services.py              # Python service launcher
├── api_server.py                  # FastAPI server
├── streamlit_app.py               # Streamlit UI
├── requirements.txt               # Base dependencies
├── requirements-api.txt           # API dependencies
├── requirements-streamlit.txt     # Streamlit dependencies
├── src/                           # Application source code
├── raw_data/                      # Input CSV files
├── processed_data/                # Output CSV files
└── neo4j/                         # Neo4j data directory
```

## 🔧 Configuration

### Environment Variables

The following environment variables can be configured:

| Variable | Default | Description |
|----------|---------|-------------|
| `NEO4J_URI` | `bolt://neo4j:7687` | Neo4j connection URI |
| `NEO4J_USERNAME` | `neo4j` | Neo4j username |
| `NEO4J_PASSWORD` | `password123` | Neo4j password |
| `SERVICES` | `api,ui` | Services to start (api, ui, or both) |
| `PYTHONPATH` | `/app` | Python path |

### Ports

| Service | Port | Description |
|---------|------|-------------|
| Neo4j HTTP | 7474 | Neo4j browser interface |
| Neo4j Bolt | 7687 | Neo4j bolt protocol |
| FastAPI | 8000 | REST API server |
| Streamlit | 8501 | Web UI |

## 🛠️ Development

### Building the Image

```bash
# Build the application image
docker build -t uniq_user_server .

# Build with no cache
docker build --no-cache -t uniq_user_server .
```

### Running Locally (without Docker)

If you prefer to run the services locally:

```bash
# Install dependencies
pip install -r requirements.txt
pip install -r requirements-api.txt
pip install -r requirements-streamlit.txt

# Start services using Python launcher
python start_services.py

# Or start individual services
python start_services.py api    # API only
python start_services.py ui     # UI only
```

### Development with Docker

```bash
# Start services in development mode
docker-compose -f docker-compose-unified.yml up --build

# Rebuild and restart
docker-compose -f docker-compose-unified.yml up --build --force-recreate

# View logs in real-time
docker-compose -f docker-compose-unified.yml logs -f
```

## 📊 Monitoring and Health Checks

### Health Check Endpoints

- **API Health**: `GET http://localhost:8000/health`
- **Neo4j Health**: `GET http://localhost:7474`

### Logs

```bash
# View all logs
docker-compose -f docker-compose-unified.yml logs

# View specific service logs
docker-compose -f docker-compose-unified.yml logs app
docker-compose -f docker-compose-unified.yml logs neo4j

# Follow logs in real-time
docker-compose -f docker-compose-unified.yml logs -f app
```

### Container Status

```bash
# Check container status
docker-compose -f docker-compose-unified.yml ps

# Check resource usage
docker stats
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   lsof -i :8000
   lsof -i :8501
   
   # Kill the process or change ports in docker-compose
   ```

2. **Neo4j Connection Issues**
   ```bash
   # Check Neo4j logs
   docker-compose -f docker-compose-unified.yml logs neo4j
   
   # Wait for Neo4j to be ready
   docker-compose -f docker-compose-unified.yml up -d neo4j
   # Wait a few minutes, then start other services
   ```

3. **Permission Issues**
   ```bash
   # Fix file permissions
   chmod +x docker-entrypoint.sh
   
   # Rebuild with no cache
   docker-compose -f docker-compose-unified.yml build --no-cache
   ```

4. **Memory Issues**
   ```bash
   # Increase Docker memory limit
   # In Docker Desktop: Settings > Resources > Memory
   ```

### Debug Mode

```bash
# Run with debug output
docker-compose -f docker-compose-unified.yml up --verbose

# Access container shell
docker exec -it uniq_user_app /bin/bash

# Check service status inside container
docker exec -it uniq_user_app ps aux
```

## 🧹 Cleanup

### Remove All Data

```bash
# Stop and remove containers
docker-compose -f docker-compose-unified.yml down

# Remove volumes (WARNING: This deletes all data)
docker-compose -f docker-compose-unified.yml down -v

# Remove images
docker rmi uniq_user_server
```

### Reset Neo4j Data

```bash
# Remove Neo4j volumes
docker volume rm uniq_user_server_neo4j_data
docker volume rm uniq_user_server_neo4j_logs
docker volume rm uniq_user_server_neo4j_import
```

## 📈 Performance Optimization

### Resource Limits

Add resource limits to `docker-compose-unified.yml`:

```yaml
services:
  app:
    # ... other config
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### Scaling

```bash
# Scale API service (if using separate services)
docker-compose up --scale api=3
```

## 🔐 Security

### Production Deployment

For production deployment:

1. **Change default passwords**
2. **Use secrets management**
3. **Enable HTTPS**
4. **Configure firewall rules**
5. **Use non-root user in container**

### Example Production Configuration

```yaml
services:
  app:
    environment:
      - NEO4J_PASSWORD_FILE=/run/secrets/neo4j_password
    secrets:
      - neo4j_password
    user: "1000:1000"

secrets:
  neo4j_password:
    file: ./secrets/neo4j_password.txt
```

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Streamlit Documentation](https://docs.streamlit.io/)
- [Neo4j Documentation](https://neo4j.com/docs/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

## 🤝 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review the logs: `docker-compose logs`
3. Check the API documentation: http://localhost:8000/docs
4. Verify Neo4j is running: http://localhost:7474 