version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:2025.05
    container_name: uniq_user_neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_directories_import=/import
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
      - neo4j_plugins:/plugins
    networks:
      - uniq_user_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7474"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Server
  api:
    build: .
    container_name: uniq_user_api
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
      - API_HOST=0.0.0.0
      - API_PORT=8008
      - SERVICES=api
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    ports:
      - "8008:8008"
    command: ["python", "api_server.py"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Streamlit UI
  ui:
    build: .
    container_name: uniq_user_ui
    depends_on:
      neo4j:
        condition: service_healthy
      api:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_PORT=8018
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - SERVICES=ui
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    ports:
      - "8018:8018"
    command: ["streamlit", "run", "streamlit_app.py", "--server.port=8018", "--server.address=0.0.0.0"]

  # Data Processing Service (optional)
  processor:
    build: .
    container_name: uniq_user_processor
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password123
      - PYTHONPATH=/app
    volumes:
      - ./processed_data:/app/processed_data
      - ./raw_data:/app/raw_data
      - ./import:/app/import
      - ./design:/app/design
    networks:
      - uniq_user_network
    command: ["python", "main.py"]

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local

networks:
  uniq_user_network:
    driver: bridge 