# CSV Processing REST API Guide

This guide shows you how to use the REST API to process CSV files for the One ID Mapping System.

## 🚀 Quick Start

### 1. Start the API Server

```bash
# Using Docker (recommended)
./start-docker.sh start-api

# Or locally
python api_server.py
```

The API server will be available at: **http://localhost:8008**

### 2. Check API Health

```bash
curl http://localhost:8008/health
```

### 3. View API Documentation

Open your browser and go to: **http://localhost:8008/docs**

## 📊 CSV Processing Endpoints

### 1. Process Raw Data (Recommended)

This endpoint processes all CSV files in the `raw_data` directory and generates two output files:
- `account_email_mapping.csv` - Account to email mappings
- `account_properties.csv` - Account properties

#### **Endpoint**: `POST /csv/process-raw`

#### **Request Body**:
```json
{
  "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
  "chunk_size": 100000
}
```

#### **Example using curl**:
```bash
curl -X POST "http://localhost:8008/csv/process-raw" \
  -H "Content-Type: application/json" \
  -d '{
    "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
    "chunk_size": 100000
  }'
```

#### **Example using Python**:
```python
import requests
import json

url = "http://localhost:8008/csv/process-raw"
data = {
    "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
    "chunk_size": 100000
}

response = requests.post(url, json=data)
result = response.json()

print(f"Success: {result['success']}")
print(f"Files processed: {result['files_processed']}")
print(f"Accounts processed: {result['accounts_processed']}")
print(f"Output files: {result['output_files']}")
```

#### **Response**:
```json
{
  "success": true,
  "files_processed": 3,
  "accounts_processed": 1500000,
  "errors_count": 0,
  "errors": [],
  "output_files": {
    "account_email_mapping": "processed_data/account_email_mapping.csv",
    "account_properties": "processed_data/account_properties.csv"
  },
  "message": "Successfully processed 3 files with 1500000 accounts",
  "timestamp": "2024-01-15T10:30:00",
  "sample_accounts": [
    {
      "account_id": "46_12345",
      "email": "<EMAIL>",
      "reg_time": "2024-01-01 10:00:00"
    }
  ]
}
```

### 2. Process Single CSV File

Process a specific CSV file and return account data.

#### **Endpoint**: `POST /csv/process`

#### **Request Body**:
```json
{
  "file_path": "raw_data/project_46.csv",
  "columns": ["account_id", "email", "reg_time"],
  "source_id": "project_46"
}
```

#### **Example using curl**:
```bash
curl -X POST "http://localhost:8008/csv/process" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "raw_data/project_46.csv",
    "columns": ["account_id", "email", "reg_time"],
    "source_id": "project_46"
  }'
```

### 3. Validate CSV File

Validate a CSV file before processing.

#### **Endpoint**: `POST /csv/validate`

#### **Request Body**:
```json
{
  "file_path": "raw_data/project_46.csv",
  "columns": ["account_id", "email", "reg_time"]
}
```

#### **Example using curl**:
```bash
curl -X POST "http://localhost:8008/csv/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "raw_data/project_46.csv",
    "columns": ["account_id", "email", "reg_time"]
  }'
```

### 4. Get CSV File Information

Get information about a CSV file.

#### **Endpoint**: `GET /csv/info?file_path=raw_data/project_46.csv`

#### **Example using curl**:
```bash
curl "http://localhost:8008/csv/info?file_path=raw_data/project_46.csv"
```

### 5. Get CSV Columns

Get column names from a CSV file.

#### **Endpoint**: `GET /csv/columns?file_path=raw_data/project_46.csv`

#### **Example using curl**:
```bash
curl "http://localhost:8008/csv/columns?file_path=raw_data/project_46.csv"
```

## 🔧 Complete Python Example

Here's a complete Python script to process CSV files:

```python
import requests
import json
import time
from typing import List, Dict, Any

class CSVProcessor:
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """Check if the API is healthy."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False
    
    def get_csv_columns(self, file_path: str) -> List[str]:
        """Get columns from a CSV file."""
        try:
            response = self.session.get(
                f"{self.base_url}/csv/columns",
                params={"file_path": file_path}
            )
            response.raise_for_status()
            result = response.json()
            return result.get("columns", [])
        except Exception as e:
            print(f"Failed to get columns: {e}")
            return []
    
    def validate_csv(self, file_path: str, columns: List[str] = None) -> Dict[str, Any]:
        """Validate a CSV file."""
        try:
            data = {"file_path": file_path}
            if columns:
                data["columns"] = columns
            
            response = self.session.post(
                f"{self.base_url}/csv/validate",
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Validation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def process_raw_data(self, columns: List[str] = None, chunk_size: int = 100000) -> Dict[str, Any]:
        """Process all CSV files in raw_data directory."""
        try:
            data = {"chunk_size": chunk_size}
            if columns:
                data["columns"] = columns
            
            print("Starting raw data processing...")
            response = self.session.post(
                f"{self.base_url}/csv/process-raw",
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Processing failed: {e}")
            return {"success": False, "error": str(e)}
    
    def process_single_file(self, file_path: str, columns: List[str] = None, source_id: str = "") -> Dict[str, Any]:
        """Process a single CSV file."""
        try:
            data = {"file_path": file_path, "source_id": source_id}
            if columns:
                data["columns"] = columns
            
            response = self.session.post(
                f"{self.base_url}/csv/process",
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Processing failed: {e}")
            return {"success": False, "error": str(e)}

def main():
    # Initialize processor
    processor = CSVProcessor()
    
    # Check API health
    if not processor.health_check():
        print("❌ API is not healthy. Please start the server first.")
        return
    
    print("✅ API is healthy")
    
    # Example 1: Process raw data (recommended)
    print("\n🔄 Processing raw data...")
    result = processor.process_raw_data(
        columns=["account_id", "email", "reg_time", "deposit_amount_sum"],
        chunk_size=100000
    )
    
    if result.get("success"):
        print(f"✅ Successfully processed {result['files_processed']} files")
        print(f"📊 Accounts processed: {result['accounts_processed']}")
        print(f"📁 Output files: {result['output_files']}")
    else:
        print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
    
    # Example 2: Validate a specific file
    print("\n🔍 Validating specific file...")
    validation = processor.validate_csv(
        file_path="raw_data/project_46.csv",
        columns=["account_id", "email", "reg_time"]
    )
    
    if validation.get("success"):
        print(f"✅ File is valid: {validation['valid_rows']}/{validation['total_rows']} rows")
    else:
        print(f"❌ Validation failed: {validation.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
```

## 📋 Common Use Cases

### 1. Process All Raw Data Files

```bash
# Using curl
curl -X POST "http://localhost:8008/csv/process-raw" \
  -H "Content-Type: application/json" \
  -d '{"chunk_size": 100000}'

# Using Python
processor = CSVProcessor()
result = processor.process_raw_data(chunk_size=100000)
```

### 2. Process with Specific Columns

```bash
# Using curl
curl -X POST "http://localhost:8008/csv/process-raw" \
  -H "Content-Type: application/json" \
  -d '{
    "columns": ["account_id", "email", "reg_time", "deposit_amount_sum"],
    "chunk_size": 50000
  }'
```

### 3. Validate Before Processing

```python
processor = CSVProcessor()

# First validate
validation = processor.validate_csv("raw_data/project_46.csv")
if validation.get("success") and validation.get("is_valid"):
    # Then process
    result = processor.process_single_file("raw_data/project_46.csv")
else:
    print("File validation failed")
```

### 4. Get File Information

```python
processor = CSVProcessor()

# Get columns
columns = processor.get_csv_columns("raw_data/project_46.csv")
print(f"Available columns: {columns}")

# Use these columns for processing
result = processor.process_raw_data(columns=columns)
```

## 🔍 Monitoring and Debugging

### Check Processing Status

```bash
# Get API status
curl http://localhost:8008/status

# Get database stats
curl http://localhost:8008/stats
```

### View Logs

```bash
# If using Docker
./start-docker.sh logs-api

# Or check container logs
docker logs uniq_user_api
```

## 🚨 Error Handling

### Common Errors and Solutions

1. **File Not Found**
   ```json
   {
     "detail": "File not found: raw_data/missing_file.csv"
   }
   ```
   **Solution**: Check file path and ensure file exists

2. **Invalid Columns**
   ```json
   {
     "detail": "Column 'invalid_column' not found in CSV"
   }
   ```
   **Solution**: Use `/csv/columns` endpoint to get available columns

3. **Memory Issues**
   ```json
   {
     "detail": "Out of memory processing large file"
   }
   ```
   **Solution**: Reduce `chunk_size` parameter

4. **API Not Available**
   ```json
   {
     "detail": "Connection refused"
   }
   ```
   **Solution**: Ensure API server is running on correct port

## 📈 Performance Tips

1. **Use appropriate chunk_size**: 
   - Small files: 10,000-50,000
   - Large files: 100,000-500,000

2. **Specify only needed columns**: Reduces memory usage

3. **Process in batches**: For very large datasets, process files individually

4. **Monitor memory usage**: Check container stats during processing

## 🔗 Related Endpoints

- **Health Check**: `GET /health`
- **API Status**: `GET /status`
- **Database Stats**: `GET /stats`
- **API Documentation**: `GET /docs`

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [API Interactive Docs](http://localhost:8008/docs)
- [Docker Setup Guide](README-Docker-Unified.md) 