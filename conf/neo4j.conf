# Neo4j configuration

server.directories.import=import
dbms.security.allow_csv_import_from_file_urls=true 

server.bolt.enabled=true
server.http.enabled=true
server.https.enabled=false


server.jvm.additional=-XX:+UseG1GC
server.jvm.additional=-XX:-OmitStackTraceInFastThrow
server.jvm.additional=-XX:+AlwaysPreTouch
server.jvm.additional=-XX:+UnlockExperimentalVMOptions
server.jvm.additional=-XX:+TrustFinalNonStaticFields
server.jvm.additional=-XX:+DisableExplicitGC
server.jvm.additional=-Djdk.nio.maxCachedBufferSize=1024
server.jvm.additional=-Dio.netty.tryReflectionSetAccessible=true
server.jvm.additional=-Dio.netty.leakDetection.level=DISABLED
server.jvm.additional=-Djdk.tls.ephemeralDHKeySize=2048
server.jvm.additional=-Djdk.tls.rejectClientInitiatedRenegotiation=true
server.jvm.additional=-XX:MaxMetaspaceSize=512m
server.jvm.additional=-XX:ReservedCodeCacheSize=256m
server.jvm.additional=-XX:-DontCompileHugeMethods

# 以下原 config 1 中的 JVM 参数被 config 2 覆盖或移除，故不保留：
# -XX:FlightRecorderOptions=stackdepth=256
# -XX:+UnlockDiagnosticVMOptions
# -XX:+DebugNonSafepoints
# --add-opens=java.base/java.nio=ALL-UNNAMED
# --add-opens=java.base/java.io=ALL-UNNAMED
# --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
# --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
# --enable-native-access=ALL-UNNAMED
# -Dlog4j2.disable.jmx=true
# -Dlog4j.layout.jsonTemplate.maxStringLength=32768
dbms.tx_state.memory_allocation=ON_HEAP
server.windows_service_name=neo4j

server.memory.heap.initial_size=4g 
server.memory.heap.max_size=4g 
server.memory.pagecache.size=1G

dbms.memory.transaction.total.max=0
db.memory.transaction.max=0

server.threads.worker_count=6 

#db.tx_log.rotation.retention_policy=1 day 1G 

server.default_listen_address=0.0.0.0
server.directories.logs=/logs
