#!/usr/bin/env python3
"""
Example configuration file showing how to use the new file configuration system.
"""

from src.config.processing_config import ProcessingConfig, FileConfig
from src.config.database_config import Neo4jConfig
from src.graph_service.graph_data_ingest import GraphDataIngest
import asyncio

async def example_usage():
    """Example of how to use the new file configuration system."""
    
    # Method 1: Use default file names
    processing_config = ProcessingConfig()
    print(f"Default account-email mapping file: {processing_config.file_config.account_email_mapping_file}")
    print(f"Default account properties file: {processing_config.file_config.account_properties_file}")
    
    # Method 2: Customize file names
    custom_file_config = FileConfig(
        account_email_mapping_file="my_account_email_mapping.csv",
        account_properties_file="my_account_properties.csv"
    )
    
    custom_processing_config = ProcessingConfig(file_config=custom_file_config)
    print(f"Custom account-email mapping file: {custom_processing_config.file_config.account_email_mapping_file}")
    print(f"Custom account properties file: {custom_processing_config.file_config.account_properties_file}")
    
    # Method 3: Initialize GraphDataIngest with configuration
    neo4j_config = Neo4jConfig(
        uri="bolt://localhost:7687",
        username="neo4j",
        password="password"
    )
    
    # Use default configuration
    graph_manager_default = GraphDataIngest(neo4j_config, processing_config)
    
    # Use custom configuration
    graph_manager_custom = GraphDataIngest(neo4j_config, custom_processing_config)
    
    # Method 4: Override file names at runtime
    await graph_manager_default.connect()
    
    # Use default files from config
    result1 = await graph_manager_default.ingest_data_from_csv()
    print(f"Used default files: {result1}")
    
    # Override with specific files
    result2 = await graph_manager_default.ingest_data_from_csv(
        account_email_mapping_file="override_account_email.csv",
        account_properties_file="override_account_properties.csv"
    )
    print(f"Used override files: {result2}")
    
    await graph_manager_default.disconnect()

if __name__ == "__main__":
    asyncio.run(example_usage()) 