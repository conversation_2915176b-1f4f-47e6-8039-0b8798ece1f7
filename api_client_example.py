#!/usr/bin/env python3
"""
Example client for the One ID Mapping System API.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any


class APIClient:
    """Client for interacting with the One ID Mapping System API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request to the API."""
        url = f"{self.base_url}{endpoint}"
        
        async with self.session.request(method, url, json=data) as response:
            response.raise_for_status()
            return await response.json()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check API health."""
        return await self._make_request("GET", "/health")
    
    async def ingest_data(self, 
                         account_email_mapping_file: str = None,
                         account_properties_file: str = None,
                         clean_before_ingestion: bool = False) -> Dict[str, Any]:
        """Ingest data from CSV files."""
        data = {
            "clean_before_ingestion": clean_before_ingestion
        }
        if account_email_mapping_file:
            data["account_email_mapping_file"] = account_email_mapping_file
        if account_properties_file:
            data["account_properties_file"] = account_properties_file
        
        return await self._make_request("POST", "/ingest", data)
    
    async def execute_pipeline(self, 
                              source_id: str,
                              enable_merging: bool = True,
                              preview_only: bool = False) -> Dict[str, Any]:
        """Execute the data processing pipeline."""
        data = {
            "source_id": source_id,
            "enable_merging": enable_merging,
            "preview_only": preview_only
        }
        return await self._make_request("POST", "/pipeline", data)
    
    async def clean_data(self) -> Dict[str, Any]:
        """Clean all data from the database."""
        return await self._make_request("POST", "/clean")
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        return await self._make_request("GET", "/stats")
    
    async def get_pipeline_status(self) -> Dict[str, Any]:
        """Get pipeline status."""
        return await self._make_request("GET", "/status")
    
    async def configure_source(self, source_id: str, file_path: str, columns: list[str] = None) -> Dict[str, Any]:
        """Configure a data source."""
        params = {
            "source_id": source_id,
            "file_path": file_path
        }
        if columns:
            params["columns"] = columns
        
        url = f"{self.base_url}/configure-source"
        async with self.session.post(url, params=params) as response:
            response.raise_for_status()
            return await response.json()
    
    async def list_sources(self) -> Dict[str, Any]:
        """List all configured sources."""
        return await self._make_request("GET", "/sources")
    
    async def process_csv(self, file_path: str, columns: list[str] = None, source_id: str = "") -> Dict[str, Any]:
        """Process a CSV file."""
        data = {
            "file_path": file_path,
            "source_id": source_id
        }
        if columns:
            data["columns"] = columns
        
        return await self._make_request("POST", "/csv/process", data)
    
    async def validate_csv(self, file_path: str, columns: list[str] = None) -> Dict[str, Any]:
        """Validate a CSV file."""
        data = {
            "file_path": file_path
        }
        if columns:
            data["columns"] = columns
        
        return await self._make_request("POST", "/csv/validate", data)
    
    async def get_csv_info(self, file_path: str) -> Dict[str, Any]:
        """Get CSV file information."""
        return await self._make_request("GET", f"/csv/info?file_path={file_path}")
    
    async def get_csv_columns(self, file_path: str) -> Dict[str, Any]:
        """Get CSV column names."""
        return await self._make_request("GET", f"/csv/columns?file_path={file_path}")
    
    async def process_raw_data(self, columns: list[str] = None, chunk_size: int = 100_000) -> Dict[str, Any]:
        """Process all CSV files from raw_data directory and save to processed_data."""
        data = {}
        if columns:
            data["columns"] = columns
        if chunk_size != 100_000:  # Only include if different from default
            data["chunk_size"] = chunk_size
        
        return await self._make_request("POST", "/csv/process-raw", data)


async def main():
    """Example usage of the API client."""
    
    async with APIClient() as client:
        print("=== One ID Mapping System API Client Example ===\n")
        
        # 1. Health check
        print("1. Checking API health...")
        try:
            health = await client.health_check()
            print(f"   Status: {health['status']}")
            print(f"   Version: {health['version']}")
            print(f"   Database connected: {health['database_connected']}")
        except Exception as e:
            print(f"   Health check failed: {e}")
            return
        
        # 2. Configure a data source
        print("\n2. Configuring data source...")
        try:
            source_config = await client.configure_source(
                source_id="example_source",
                file_path="processed_data/account_properties.csv",
                columns=["account_id", "reg_time", "deposit_amount_sum"]
            )
            print(f"   {source_config['message']}")
        except Exception as e:
            print(f"   Source configuration failed: {e}")
        
        # 3. Get pipeline status
        print("\n3. Getting pipeline status...")
        try:
            status = await client.get_pipeline_status()
            print(f"   Status: {status['status']}")
            print(f"   Database connected: {status['database_connected']}")
            print(f"   Configured sources: {status['configured_sources']}")
            print(f"   Sources: {status['sources']}")
        except Exception as e:
            print(f"   Status check failed: {e}")
        
        # 4. Ingest data (example)
        print("\n4. Ingesting data...")
        try:
            ingestion_result = await client.ingest_data(
                account_email_mapping_file="processed_data/account_email_mapping.csv",
                account_properties_file="processed_data/account_properties.csv",
                clean_before_ingestion=True
            )
            print(f"   Success: {ingestion_result['success']}")
            print(f"   Accounts created: {ingestion_result['accounts_created']}")
            print(f"   Emails created: {ingestion_result['emails_created']}")
            print(f"   Relationships created: {ingestion_result['relationships_created']}")
            print(f"   Message: {ingestion_result['message']}")
        except Exception as e:
            print(f"   Data ingestion failed: {e}")
        
        # 5. Get database statistics
        print("\n5. Getting database statistics...")
        try:
            stats = await client.get_database_stats()
            print(f"   Account count: {stats['account_count']}")
            print(f"   Email count: {stats['email_count']}")
            print(f"   Relationship count: {stats['relationship_count']}")
            print(f"   Sample accounts: {stats['sample_accounts']}")
        except Exception as e:
            print(f"   Failed to get stats: {e}")
        
        # 6. Execute pipeline (preview mode)
        print("\n6. Executing pipeline in preview mode...")
        try:
            pipeline_result = await client.execute_pipeline(
                source_id="example_source",
                preview_only=True
            )
            print(f"   Success: {pipeline_result['success']}")
            print(f"   Accounts processed: {pipeline_result['accounts_processed']}")
            print(f"   Message: {pipeline_result['message']}")
        except Exception as e:
            print(f"   Pipeline execution failed: {e}")
        
        # 7. List sources
        print("\n7. Listing configured sources...")
        try:
            sources = await client.list_sources()
            print(f"   Sources: {sources['sources']}")
            print(f"   Count: {sources['count']}")
        except Exception as e:
            print(f"   Failed to list sources: {e}")
        
        # 8. CSV Processing Examples
        print("\n8. CSV Processing Examples...")
        
        # Get CSV info
        try:
            csv_info = await client.get_csv_info("processed_data/account_properties.csv")
            print(f"   CSV Info:")
            print(f"     File: {csv_info['file_path']}")
            print(f"     Size: {csv_info['file_size']} bytes")
            print(f"     Rows: {csv_info['total_rows']}")
            print(f"     Columns: {csv_info['columns']}")
        except Exception as e:
            print(f"   Failed to get CSV info: {e}")
        
        # Validate CSV
        try:
            validation = await client.validate_csv(
                file_path="processed_data/account_properties.csv",
                columns=["account_id", "reg_time", "deposit_amount_sum"]
            )
            print(f"   CSV Validation:")
            print(f"     Valid: {validation['is_valid']}")
            print(f"     Total rows: {validation['total_rows']}")
            print(f"     Available columns: {validation['available_columns']}")
            if validation['validation_errors']:
                print(f"     Errors: {validation['validation_errors']}")
        except Exception as e:
            print(f"   Failed to validate CSV: {e}")
        
        # Process CSV
        try:
            processing = await client.process_csv(
                file_path="processed_data/account_properties.csv",
                columns=["account_id", "reg_time", "deposit_amount_sum"],
                source_id="api_example"
            )
            print(f"   CSV Processing:")
            print(f"     Success: {processing['success']}")
            print(f"     Accounts processed: {processing['accounts_processed']}")
            print(f"     Message: {processing['message']}")
            if processing['sample_accounts']:
                print(f"     Sample accounts: {processing['sample_accounts']}")
        except Exception as e:
            print(f"   Failed to process CSV: {e}")
        
        # Process Raw Data
        try:
            raw_processing = await client.process_raw_data(
                columns=["account_id", "email", "deposit_amount_sum", "reg_time"]
            )
            print(f"   Raw Data Processing:")
            print(f"     Success: {raw_processing['success']}")
            print(f"     Files processed: {raw_processing['files_processed']}")
            print(f"     Accounts processed: {raw_processing['accounts_processed']}")
            print(f"     Output files: {raw_processing['output_files']}")
            print(f"     Message: {raw_processing['message']}")
            if raw_processing['sample_accounts']:
                print(f"     Sample accounts: {raw_processing['sample_accounts']}")
        except Exception as e:
            print(f"   Failed to process raw data: {e}")
        
        print("\n=== Example completed ===")


if __name__ == "__main__":
    asyncio.run(main()) 