#!/bin/bash
set -e

# Load environment variables from .env file if it exists
if [ -f ".env" ]; then
    echo "Loading environment variables from .env file..."
    export $(grep -v '^#' .env | xargs)
    echo "Environment variables loaded."
else
    echo "Warning: .env file not found"
fi

# Debug: Print environment variables (without showing password)
echo "Debug: Environment variables:"
echo "NEO4J_URI: ${NEO4J_URI:-Not set}"
echo "NEO4J_USERNAME: ${NEO4J_USERNAME:-Not set}"
echo "NEO4J_PASSWORD: $([ -n "$NEO4J_PASSWORD" ] && echo "***" || echo "Not set")"

# Function to print colored output
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Function to wait for Neo4j to be ready
wait_for_neo4j() {
    print_info "Checking Neo4j configuration..."

    # Check if NEO4J_URI is set
    if [ -z "$NEO4J_URI" ]; then
        print_warning "NEO4J_URI not set, skipping Neo4j health check"
        return 0
    fi

    # Extract host and port from NEO4J_URI
    NEO4J_HOST=$(echo $NEO4J_URI | sed 's|bolt://||' | cut -d: -f1)
    NEO4J_PORT=$(echo $NEO4J_URI | sed 's|bolt://||' | cut -d: -f2)

    # Skip health check for localhost (external Neo4j)
    if [ "$NEO4J_HOST" = "localhost" ] || [ "$NEO4J_HOST" = "127.0.0.1" ]; then
        print_info "Neo4j is configured for external host ($NEO4J_HOST:$NEO4J_PORT)"
        print_info "Skipping health check - assuming Neo4j is running on host machine"
        return 0
    fi
    
    # Wait for Neo4j to be ready
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $NEO4J_HOST $NEO4J_PORT 2>/dev/null; then
            print_success "Neo4j is ready!"
            return 0
        fi
        
        print_info "Attempt $attempt/$max_attempts: Neo4j not ready yet, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Neo4j failed to become ready after $max_attempts attempts"
    return 1
}

# Function to start API server
start_api_server() {
    print_info "Starting FastAPI server..."
    
    if [ ! -f "api_server.py" ]; then
        print_error "api_server.py not found!"
        return 1
    fi
    
    # Start API server in background
    python api_server.py &
    API_PID=$!
    
    # Wait a moment to check if it started successfully
    sleep 3
    
    if kill -0 $API_PID 2>/dev/null; then
        print_success "FastAPI server started (PID: $API_PID)"
        print_info "🔗 API Server: http://localhost:8008"
        print_info "📚 API Docs: http://localhost:8008/docs"
        return 0
    else
        print_error "FastAPI server failed to start"
        return 1
    fi
}

# Function to start Streamlit server
start_streamlit_server() {
    print_info "Starting Streamlit server..."
    
    if [ ! -f "streamlit_app.py" ]; then
        print_error "streamlit_app.py not found!"
        return 1
    fi
    
    # Start Streamlit server in background
    streamlit run streamlit_app.py \
        --server.port=8018 \
        --server.address=0.0.0.0 \
        --browser.gatherUsageStats=false &
    STREAMLIT_PID=$!
    
    # Wait a moment to check if it started successfully
    sleep 5
    
    if kill -0 $STREAMLIT_PID 2>/dev/null; then
        print_success "Streamlit server started (PID: $STREAMLIT_PID)"
        print_info "🌐 Streamlit UI: http://localhost:8018"
        return 0
    else
        print_error "Streamlit server failed to start"
        return 1
    fi
}

# Function to handle shutdown
cleanup() {
    print_info "Shutting down services..."
    
    if [ ! -z "$API_PID" ]; then
        print_info "Stopping FastAPI server (PID: $API_PID)..."
        kill $API_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$STREAMLIT_PID" ]; then
        print_info "Stopping Streamlit server (PID: $STREAMLIT_PID)..."
        kill $STREAMLIT_PID 2>/dev/null || true
    fi
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main execution
print_info "🚀 One ID Mapping System - Docker Entrypoint"
print_info "=============================================="

# Wait for Neo4j if needed
wait_for_neo4j

# Determine which services to start
SERVICES=${1:-${SERVICES:-"api,ui"}}
print_info "Starting services: $SERVICES"

# Start API server if requested
if [[ $SERVICES == *"api"* ]]; then
    if ! start_api_server; then
        print_error "Failed to start API server"
        exit 1
    fi
fi

# Start Streamlit server if requested
if [[ $SERVICES == *"ui"* ]]; then
    if ! start_streamlit_server; then
        print_error "Failed to start Streamlit server"
        exit 1
    fi
fi

print_success "🎉 All requested services started successfully!"
print_info "⏹️  Press Ctrl+C to stop all services"

# Keep the container running and monitor services
while true; do
    # Check if API server is still running
    if [ ! -z "$API_PID" ] && ! kill -0 $API_PID 2>/dev/null; then
        print_error "API server has stopped unexpectedly"
        exit 1
    fi
    
    # Check if Streamlit server is still running
    if [ ! -z "$STREAMLIT_PID" ] && ! kill -0 $STREAMLIT_PID 2>/dev/null; then
        print_error "Streamlit server has stopped unexpectedly"
        exit 1
    fi
    
    sleep 5
done 