# Streamlit Neo4j Query Interface

A modern web-based interface for querying the One ID Mapping System's Neo4j database. This Streamlit application provides an intuitive way to explore account data, project information, and database statistics.

## Features

### 🔍 Account Query Interface
- **Account by ID**: Query specific accounts using separate project_id and original_account_id inputs
- **Accounts by Project ID**: Find all accounts belonging to a specific project (e.g., `60`)
- **Database Statistics**: View comprehensive database overview and metrics

### 📊 Data Visualization
- **Local Properties**: View account-specific data (registration time, deposits, bets, etc.)
- **Global Properties**: View account data with related information (emails, relationships)
- **Project Summary**: Aggregate statistics for project-based queries
- **Database Stats**: Node and relationship counts

### 🎨 Modern UI
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Live data refresh and connection status
- **Interactive Elements**: Dropdowns, buttons, and form inputs
- **Professional Styling**: Clean, modern interface with custom CSS

## Installation

### Prerequisites
- Python 3.8 or higher
- Neo4j database running and accessible
- One ID Mapping System data loaded into Neo4j

### Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements-streamlit.txt
   ```

2. **Verify Neo4j Connection**:
   - Ensure Neo4j is running on your system
   - Note your Neo4j connection details (URI, username, password)

3. **Run the Application**:
   ```bash
   # Option 1: Use the launcher script
   python run_streamlit.py
   
   # Option 2: Run directly with streamlit
   streamlit run streamlit_app.py
   ```

## Usage

### 1. Connect to Neo4j
1. Open the web interface (usually at `http://localhost:8501`)
2. Use the sidebar to enter your Neo4j connection details:
   - **Neo4j URI**: `bolt://localhost:7687` (default)
   - **Username**: `neo4j` (default)
   - **Password**: Your Neo4j password
3. Click "Connect to Neo4j"

### 2. Query Account by ID
1. Select "Account by ID" from the query type dropdown
2. Enter the **Project ID** (e.g., `60`)
3. Enter the **Original Account ID** (e.g., `********`)
4. Click "Query Account"
5. View results in two sections:
   - **Local Properties**: Account-specific data
   - **Global Properties**: Account data with related emails

**Note**: The system automatically combines the inputs as `60_********` for the Neo4j query.

### 3. Query Accounts by Project ID
1. Select "Accounts by Project ID" from the query type dropdown
2. Enter the project ID (e.g., `60`)
3. Click "Query Project Accounts"
4. View:
   - List of all accounts for the project
   - Summary statistics (total accounts, deposits, bets, withdrawals)

### 4. View Database Statistics
1. Select "Database Statistics" from the query type dropdown
2. Click "Refresh Database Statistics"
3. View node and relationship counts

## Data Structure

The interface expects the following Neo4j data structure:

### Account Nodes (`:account`)
```cypher
{
  account_id: "60_272319",           // Format: project_id_original_id
  reg_time: "2024-06-03T14:33:56",   // Registration timestamp
  deposit_amount_sum: 3.0,           // Total deposits
  deposit_times_sum: 1,              // Number of deposits
  bet_money_sum: 10.0,               // Total bets
  withdraw_amount_sum: 0.0,          // Total withdrawals
  current_money: 5.0,                // Current balance
  active_time: "2024-12-20T10:30:00", // Last activity
  update_time: "2024-12-20T10:30:00"  // Last update
}
```

### Email Nodes (`:email`)
```cypher
{
  email: "<EMAIL>"          // Email address
}
```

### Relationships
```cypher
(account)-[:HAS_EMAIL]->(email)      // Account to email relationship
```

## Query Examples

### Find Account by ID
```cypher
-- Input: project_id = "60", original_account_id = "********"
-- Combined: account_id = "60_********"
MATCH (a:account {account_id: "60_********"})
RETURN a.account_id, a.reg_time, a.deposit_amount_sum
```

### Find Accounts by Project
```cypher
-- Input: project_id = "60"
-- Searches for all accounts starting with "60_"
MATCH (a:account)
WHERE a.account_id STARTS WITH "60_"
RETURN a.account_id, a.reg_time, a.deposit_amount_sum
ORDER BY a.account_id
```

### Get Account with Emails
```cypher
-- Input: project_id = "60", original_account_id = "********"
-- Combined: account_id = "60_********"
MATCH (a:account {account_id: "60_********"})
OPTIONAL MATCH (a)-[:HAS_EMAIL]->(e:email)
RETURN a.account_id, collect(e.email) as emails
```

## Configuration

### Environment Variables
You can set these environment variables for default connection settings:

```bash
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USERNAME="neo4j"
export NEO4J_PASSWORD="your_password"
```

### Customization
The interface can be customized by modifying:
- `streamlit_app.py`: Main application logic
- CSS styles in the `st.markdown` section
- Query methods in the `Neo4jQueryInterface` class

## Troubleshooting

### Connection Issues
- **"Connection failed"**: Check Neo4j URI, username, and password
- **"Connection timeout"**: Verify Neo4j is running and accessible
- **"Authentication failed"**: Check username/password combination

### Data Issues
- **"Account not found"**: Verify the project_id and original_account_id combination exists
- **"No accounts for project"**: Check if the project ID is correct
- **"Empty results"**: Ensure data has been loaded into Neo4j

### Performance Issues
- **Slow queries**: Consider adding Neo4j indexes on `account_id`
- **Large result sets**: Queries are limited to 100 results for performance
- **Memory usage**: Close unused browser tabs to free memory

## Development

### Adding New Query Types
1. Add a new option to the query type dropdown
2. Implement the query logic in `Neo4jQueryInterface`
3. Add the UI handling in the main function
4. Test with sample data

### Styling Changes
Modify the CSS in the `st.markdown` section:
```python
st.markdown("""
<style>
    .custom-class {
        /* Your CSS here */
    }
</style>
""", unsafe_allow_html=True)
```

### Database Schema Changes
If the Neo4j schema changes, update:
1. Query methods in `Neo4jQueryInterface`
2. Data display logic in the main function
3. Documentation in this README

## Security Considerations

- **Connection Security**: Use secure Neo4j connections (TLS/SSL)
- **Authentication**: Use strong passwords for Neo4j
- **Network Access**: Restrict access to the Streamlit interface
- **Data Privacy**: Ensure sensitive data is properly protected

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Neo4j logs for database errors
3. Check Streamlit logs for application errors
4. Create an issue in the repository

## License

This interface is part of the One ID Mapping System and follows the same license terms.