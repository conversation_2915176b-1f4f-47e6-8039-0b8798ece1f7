#!/usr/bin/env python3
"""
Unified service launcher for One ID Mapping System.
Launches both FastAPI and Streamlit services with proper process management.
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
from pathlib import Path
from typing import List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceManager:
    """Manages multiple services with proper startup/shutdown."""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = False
        
        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down services...")
        self.shutdown()
        sys.exit(0)
    
    def start_api_server(self) -> Optional[subprocess.Popen]:
        """Start the FastAPI server."""
        try:
            logger.info("Starting FastAPI server...")
            
            # Check if api_server.py exists
            if not Path("api_server.py").exists():
                logger.error("api_server.py not found!")
                return None
            
            # Start the API server
            process = subprocess.Popen([
                sys.executable, "api_server.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait a moment to check if it started successfully
            time.sleep(2)
            if process.poll() is None:
                logger.info("✅ FastAPI server started successfully")
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ FastAPI server failed to start: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to start FastAPI server: {e}")
            return None
    
    def start_streamlit_server(self) -> Optional[subprocess.Popen]:
        """Start the Streamlit server."""
        try:
            logger.info("Starting Streamlit server...")
            
            # Check if streamlit_app.py exists
            if not Path("streamlit_app.py").exists():
                logger.error("streamlit_app.py not found!")
                return None
            
            # Start the Streamlit server
            process = subprocess.Popen([
                sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
                "--server.port", "8501",
                "--server.address", "0.0.0.0",
                "--browser.gatherUsageStats", "false"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait a moment to check if it started successfully
            time.sleep(3)
            if process.poll() is None:
                logger.info("✅ Streamlit server started successfully")
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ Streamlit server failed to start: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to start Streamlit server: {e}")
            return None
    
    def start_services(self, services: List[str] = None):
        """Start specified services or all services."""
        if services is None:
            services = ["api", "ui"]
        
        logger.info(f"Starting services: {', '.join(services)}")
        
        # Start API server if requested
        if "api" in services:
            api_process = self.start_api_server()
            if api_process:
                self.processes.append(api_process)
                logger.info("🔗 API Server: http://localhost:8000")
                logger.info("📚 API Docs: http://localhost:8000/docs")
            else:
                logger.error("Failed to start API server")
                return False
        
        # Start Streamlit server if requested
        if "ui" in services:
            ui_process = self.start_streamlit_server()
            if ui_process:
                self.processes.append(ui_process)
                logger.info("🌐 Streamlit UI: http://localhost:8501")
            else:
                logger.error("Failed to start Streamlit server")
                return False
        
        self.running = True
        logger.info("🎉 All services started successfully!")
        logger.info("⏹️  Press Ctrl+C to stop all services")
        
        return True
    
    def monitor_services(self):
        """Monitor running services and restart if needed."""
        while self.running:
            time.sleep(5)
            
            # Check if any process has died
            for i, process in enumerate(self.processes):
                if process.poll() is not None:
                    logger.warning(f"Service {i} has stopped unexpectedly")
                    # Could implement restart logic here if needed
    
    def shutdown(self):
        """Shutdown all running services."""
        logger.info("Shutting down services...")
        self.running = False
        
        for i, process in enumerate(self.processes):
            try:
                logger.info(f"Stopping service {i}...")
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"Service {i} stopped")
            except subprocess.TimeoutExpired:
                logger.warning(f"Service {i} did not stop gracefully, forcing...")
                process.kill()
            except Exception as e:
                logger.error(f"Error stopping service {i}: {e}")
        
        logger.info("All services stopped")


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ["fastapi", "uvicorn", "streamlit", "pandas", "neo4j"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Please install missing packages:")
        logger.info("pip install -r requirements.txt")
        logger.info("pip install -r requirements-api.txt")
        logger.info("pip install -r requirements-streamlit.txt")
        return False
    
    return True


def main():
    """Main entry point."""
    print("🚀 One ID Mapping System - Service Launcher")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Parse command line arguments
    services = None
    if len(sys.argv) > 1:
        services = sys.argv[1:]
        valid_services = ["api", "ui"]
        invalid_services = [s for s in services if s not in valid_services]
        if invalid_services:
            logger.error(f"Invalid services: {', '.join(invalid_services)}")
            logger.info(f"Valid services: {', '.join(valid_services)}")
            sys.exit(1)
    
    # Create service manager
    manager = ServiceManager()
    
    # Start services
    if not manager.start_services(services):
        logger.error("Failed to start services")
        sys.exit(1)
    
    try:
        # Monitor services
        manager.monitor_services()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        manager.shutdown()


if __name__ == "__main__":
    main() 