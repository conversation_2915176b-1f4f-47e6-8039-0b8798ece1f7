# One ID Mapping System - Simple Docker Setup

This document describes how to run the One ID Mapping System using a single Docker container that hosts both the FastAPI backend and Streamlit frontend.

## 🚀 Quick Start

### Prerequisites

- Docker installed and running on your system
- Raw data files in the `raw_data` directory
- Processed data will be stored in the `processed_data` directory

### 1. Build and Run

Simply run the provided script:

```bash
./run-docker.sh
```

This will:
- Build the Docker image
- Create necessary directories (`raw_data`, `processed_data`)
- Start a container with both services
- Mount the data directories from your local filesystem

### 2. Access the Services

Once the container is running, you can access:

- **FastAPI Server**: http://localhost:8008
- **FastAPI Documentation**: http://localhost:8008/docs
- **Streamlit UI**: http://localhost:8018

### 3. Stop the Services

To stop and remove the container:

```bash
./stop-docker.sh
```

## 📋 Service Details

### Port Configuration

- **FastAPI**: Port 8008 (changed from default 8000)
- **Streamlit**: Port 8018 (changed from default 8501)

### Volume Mounts

The following directories are mounted from your local filesystem:

- `./raw_data` → `/app/raw_data` (input data files)
- `./processed_data` → `/app/processed_data` (output data files)

This means any files you place in the local `raw_data` directory will be accessible to the container, and any processed files will be saved back to your local `processed_data` directory.

## 🔧 Manual Docker Commands

If you prefer to run Docker commands manually:

### Build the Image

```bash
docker build -t uniq_user_server:latest .
```

### Run the Container

```bash
docker run -d \
    --name uniq_user_server \
    -p 8008:8008 \
    -p 8018:8018 \
    -v "$(pwd)/raw_data:/app/raw_data" \
    -v "$(pwd)/processed_data:/app/processed_data" \
    -e PYTHONPATH=/app \
    uniq_user_server:latest
```

### Stop and Remove

```bash
docker stop uniq_user_server
docker rm uniq_user_server
```

## 🛠️ Troubleshooting

### View Container Logs

```bash
docker logs uniq_user_server
```

### Access Container Shell

```bash
docker exec -it uniq_user_server /bin/bash
```

### Check Container Status

```bash
docker ps
```

### Test Environment Variables

To test if environment variables are loaded correctly:

```bash
docker exec -it uniq_user_server python test_env.py
```

### Debug Streamlit Connection Issues

If Streamlit can't connect to Neo4j:

1. Check the container logs for environment variable loading:
   ```bash
   docker logs uniq_user_server | grep -i neo4j
   ```

2. Verify your `.env` file has the correct Neo4j credentials:
   ```bash
   cat .env
   ```

3. Test the connection inside the container:
   ```bash
   docker exec -it uniq_user_server python test_env.py
   ```

### Rebuild After Changes

If you make changes to the code, you need to rebuild the image:

```bash
./stop-docker.sh
./run-docker.sh
```

## 📁 Project Structure

```
uniq_user_server/
├── .env                      # Environment configuration (copied to container)
├── Dockerfile                # Docker image configuration
├── docker-entrypoint.sh     # Container startup script
├── requirements.txt          # Unified Python dependencies
├── run-docker.sh            # Build and run script
├── stop-docker.sh           # Stop and cleanup script
├── api_server.py            # FastAPI server (port 8008)
├── streamlit_app.py         # Streamlit UI
├── run_streamlit.py         # Streamlit runner (port 8018)
├── raw_data/                # Input data directory (mounted)
├── processed_data/          # Output data directory (mounted)
└── src/                     # Application source code
```

## 🔄 Development Workflow

1. Make changes to your code
2. Stop the current container: `./stop-docker.sh`
3. Rebuild and start: `./run-docker.sh`
4. Test your changes at the service URLs

## 📊 Environment Variables

The container reads configuration from the `.env` file which includes:

- `NEO4J_URI`: Neo4j database connection string
- `NEO4J_USERNAME`: Neo4j username
- `NEO4J_PASSWORD`: Neo4j password
- `SECRET_KEY`: Application secret key
- `DEBUG`: Debug mode setting
- `LOG_LEVEL`: Logging level
- `MERGE_CONFIDENCE_THRESHOLD`: User merging confidence threshold
- `BATCH_SIZE`: Processing batch size
- `MAX_WORKERS`: Maximum worker threads

The `.env` file is automatically copied into the Docker container during build.

## 🎯 Key Features

- **Single Container**: Both FastAPI and Streamlit run in one container
- **Custom Ports**: FastAPI on 8008, Streamlit on 8018
- **Volume Mounting**: Direct access to local data directories
- **Unified Dependencies**: Single requirements.txt file
- **Easy Management**: Simple start/stop scripts
- **Development Friendly**: Quick rebuild and restart process
