"""
Database configuration classes for the One ID Mapping System.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class Neo4jConfig:
    """Neo4j database configuration."""
    
    uri: str
    username: str
    password: str
    database: str = "neo4j"
    max_connection_pool_size: int = 50
    connection_timeout: int = 30
    max_transaction_retry_time: int = 15
    
    def __post_init__(self):
        """Validate Neo4j configuration."""
        if not self.uri:
            raise ValueError("Neo4j URI is required")
        if not self.username:
            raise ValueError("Neo4j username is required")
        if not self.password:
            raise ValueError("Neo4j password is required")


@dataclass
class PostgreSQLConfig:
    """PostgreSQL database configuration."""
    
    host: str
    port: int
    username: str
    password: str
    database: str
    min_connections: int = 1
    max_connections: int = 20
    connection_timeout: int = 30
    
    def __post_init__(self):
        """Validate PostgreSQL configuration."""
        if not self.host:
            raise ValueError("PostgreSQL host is required")
        if not (1 <= self.port <= 65535):
            raise ValueError("PostgreSQL port must be between 1 and 65535")
        if not self.username:
            raise ValueError("PostgreSQL username is required")
        if not self.password:
            raise ValueError("PostgreSQL password is required")
        if not self.database:
            raise ValueError("PostgreSQL database name is required")


@dataclass
class RedisConfig:
    """Redis configuration."""
    
    host: str
    port: int
    password: Optional[str] = None
    database: int = 0
    max_connections: int = 10
    connection_timeout: int = 5
    socket_timeout: int = 5
    
    def __post_init__(self):
        """Validate Redis configuration."""
        if not self.host:
            raise ValueError("Redis host is required")
        if not (1 <= self.port <= 65535):
            raise ValueError("Redis port must be between 1 and 65535")
        if not (0 <= self.database <= 15):
            raise ValueError("Redis database must be between 0 and 15")


@dataclass
class DatabaseConfig:
    """Main database configuration container."""
    
    neo4j: Neo4jConfig
    postgresql: Optional[PostgreSQLConfig] = None
    redis: RedisConfig = None
    
    def __post_init__(self):
        """Validate database configuration."""
        if not self.neo4j:
            raise ValueError("Neo4j configuration is required") 