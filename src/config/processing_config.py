"""
Processing configuration classes for the One ID Mapping System.
"""

from dataclasses import dataclass, field
from typing import Dict, List


@dataclass
class AggregationRule:
    """Rule for aggregating a specific field."""
    
    aggregation_type: str  # 'sum', 'max', 'min', 'latest', 'earliest', 'collect'
    field_category: str    # 'financial', 'time', 'count', 'state', 'identity'
    weight: float = 1.0
    
    def __post_init__(self):
        """Validate aggregation rule."""
        valid_types = ['sum', 'max', 'min', 'latest', 'earliest', 'collect']
        if self.aggregation_type not in valid_types:
            raise ValueError(f"Invalid aggregation_type: {self.aggregation_type}")
        
        valid_categories = ['financial', 'time', 'count', 'state', 'identity']
        if self.field_category not in valid_categories:
            raise ValueError(f"Invalid field_category: {self.field_category}")
        
        if not (0.0 <= self.weight <= 1.0):
            raise ValueError("Weight must be between 0.0 and 1.0")


@dataclass
class FileConfig:
    """Configuration for data files."""
    
    account_email_mapping_file: str = "account_email_mapping.csv"
    account_properties_file: str = "account_properties.csv"
    
    def __post_init__(self):
        """Validate file configuration."""
        if not self.account_email_mapping_file:
            raise ValueError("account_email_mapping_file cannot be empty")
        if not self.account_properties_file:
            raise ValueError("account_properties_file cannot be empty")


@dataclass
class ProcessingConfig:
    """Configuration for data processing."""
    
    batch_size: int = 1000
    max_workers: int = 4
    retry_attempts: int = 3
    retry_delay: int = 5
    merge_confidence_threshold: float = 0.8
    enable_parallel_processing: bool = True
    max_memory_usage_mb: int = 1024
    file_config: FileConfig = None
    
    def __post_init__(self):
        """Validate processing configuration and initialize file config."""
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        if self.max_workers <= 0:
            raise ValueError("max_workers must be positive")
        if self.retry_attempts < 0:
            raise ValueError("retry_attempts cannot be negative")
        if self.retry_delay < 0:
            raise ValueError("retry_delay cannot be negative")
        if not (0.0 <= self.merge_confidence_threshold <= 1.0):
            raise ValueError("merge_confidence_threshold must be between 0.0 and 1.0")
        if self.max_memory_usage_mb <= 0:
            raise ValueError("max_memory_usage_mb must be positive")
        
        # Initialize file config if not provided
        if self.file_config is None:
            self.file_config = FileConfig()
    
    def __post_init__(self):
        """Validate processing configuration."""
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        if self.max_workers <= 0:
            raise ValueError("max_workers must be positive")
        if self.retry_attempts < 0:
            raise ValueError("retry_attempts cannot be negative")
        if self.retry_delay < 0:
            raise ValueError("retry_delay cannot be negative")
        if not (0.0 <= self.merge_confidence_threshold <= 1.0):
            raise ValueError("merge_confidence_threshold must be between 0.0 and 1.0")
        if self.max_memory_usage_mb <= 0:
            raise ValueError("max_memory_usage_mb must be positive")


@dataclass
class AggregationRules:
    """Collection of aggregation rules for different fields."""
    
    rules: Dict[str, AggregationRule] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize default aggregation rules if none provided."""
        if not self.rules:
            self.rules = {
                'deposit_amount_sum': AggregationRule('sum', 'financial'),
                'deposit_times_sum': AggregationRule('sum', 'count'),
                'bet_money_sum': AggregationRule('sum', 'financial'),
                'withdraw_amount_sum': AggregationRule('sum', 'financial'),
                'active_time': AggregationRule('max', 'time'),
                'reg_time': AggregationRule('min', 'time'),
                'current_money': AggregationRule('latest', 'state'),
                'update_time': AggregationRule('max', 'time'),
                'email': AggregationRule('collect', 'identity')
            }
    
    def get_rule(self, field_name: str) -> AggregationRule:
        """Get aggregation rule for a specific field."""
        return self.rules.get(field_name)
    
    def add_rule(self, field_name: str, rule: AggregationRule):
        """Add or update an aggregation rule."""
        self.rules[field_name] = rule
    
    def remove_rule(self, field_name: str):
        """Remove an aggregation rule."""
        if field_name in self.rules:
            del self.rules[field_name]
    
    def get_fields_by_category(self, category: str) -> List[str]:
        """Get all fields for a specific category."""
        return [field_name for field_name, rule in self.rules.items() 
                if rule.field_category == category]
    
    def to_dict(self) -> Dict[str, Dict[str, str]]:
        """Convert rules to dictionary for serialization."""
        return {
            field_name: {
                'aggregation_type': rule.aggregation_type,
                'field_category': rule.field_category,
                'weight': rule.weight
            }
            for field_name, rule in self.rules.items()
        } 