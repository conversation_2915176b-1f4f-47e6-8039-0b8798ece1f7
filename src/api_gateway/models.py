"""
API models for the One ID Mapping System Gateway.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class DataIngestionRequest(BaseModel):
    """Request model for data ingestion."""
    
    account_email_mapping_file: Optional[str] = Field(
        None, 
        description="Path to account-email mapping CSV file (optional, uses config default if not provided)"
    )
    account_properties_file: Optional[str] = Field(
        None, 
        description="Path to account properties CSV file (optional, uses config default if not provided)"
    )
    clean_before_ingestion: bool = Field(
        False, 
        description="Whether to clean all existing data before ingestion"
    )


class DataIngestionResponse(BaseModel):
    """Response model for data ingestion."""
    
    success: bool
    accounts_created: int
    emails_created: int
    relationships_created: int
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    errors: Optional[List[str]] = None


class PipelineExecutionRequest(BaseModel):
    """Request model for pipeline execution."""
    
    source_id: str = Field(..., description="Source ID to process")
    enable_merging: bool = Field(True, description="Whether to enable user merging")
    preview_only: bool = Field(False, description="Whether to run in preview mode only")


class PipelineExecutionResponse(BaseModel):
    """Response model for pipeline execution."""
    
    success: bool
    source_id: str
    accounts_processed: int
    users_created: int
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    errors: Optional[List[str]] = None
    preview_data: Optional[Dict[str, Any]] = None


class DataCleaningResponse(BaseModel):
    """Response model for data cleaning."""
    
    success: bool
    nodes_deleted: int
    relationships_deleted: int
    remaining_nodes: int
    remaining_relationships: int
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)


class DatabaseStatsResponse(BaseModel):
    """Response model for database statistics."""
    
    account_count: int
    email_count: int
    relationship_count: int
    sample_accounts: List[str]
    timestamp: datetime = Field(default_factory=datetime.now)


class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status."""
    
    status: str
    database_connected: bool
    configured_sources: int
    sources: List[str]
    timestamp: datetime = Field(default_factory=datetime.now)
    error: Optional[str] = None


class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    
    status: str
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str
    database_connected: bool


class CSVProcessingRequest(BaseModel):
    """Request model for CSV processing."""
    
    file_path: str = Field(..., description="Path to the CSV file to process")
    columns: Optional[List[str]] = Field(None, description="Specific columns to process (optional)")
    source_id: str = Field("", description="Source ID for the processed data")


class CSVProcessingResponse(BaseModel):
    """Response model for CSV processing."""
    
    success: bool
    accounts_processed: int
    errors_count: int
    errors: List[str]
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    sample_accounts: Optional[List[Dict[str, Any]]] = None


class RawDataProcessingRequest(BaseModel):
    """Request model for processing raw data from raw_data directory."""
    
    columns: Optional[List[str]] = Field(None, description="Specific columns to process (optional)")
    chunk_size: Optional[int] = Field(100_000, description="Number of rows to process at once for large files (default: 100,000)")


class RawDataProcessingResponse(BaseModel):
    """Response model for raw data processing."""
    
    success: bool
    files_processed: int
    accounts_processed: int
    errors_count: int
    errors: List[str]
    output_files: Dict[str, str]  # Dictionary with both file paths
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    sample_accounts: Optional[List[Dict[str, Any]]] = None


class CSVValidationRequest(BaseModel):
    """Request model for CSV validation."""
    
    file_path: str = Field(..., description="Path to the CSV file to validate")
    columns: Optional[List[str]] = Field(None, description="Specific columns to validate")


class CSVValidationResponse(BaseModel):
    """Response model for CSV validation."""
    
    success: bool
    is_valid: bool
    total_rows: int
    valid_rows: int
    invalid_rows: int
    validation_errors: List[str]
    available_columns: List[str]
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)


class CSVInfoResponse(BaseModel):
    """Response model for CSV file information."""
    
    file_path: str
    file_size: int
    total_rows: int
    columns: List[str]
    sample_data: List[Dict[str, str]]
    timestamp: datetime = Field(default_factory=datetime.now) 