"""
API Service for the One ID Mapping System.
"""

import logging
from typing import Optional, Dict, Any

from ..config.settings import get_settings
from ..config.database_config import Neo4jConfig
from ..config.processing_config import ProcessingConfig
from ..data_ingestion.ingestion_service import DataIngestionService
from ..data_ingestion.csv_processor import CSVDataProcessor
from ..user_merging.merging_service import UserMergingService
from ..graph_service.graph_data_ingest import GraphDataIngest
from ..data_pipeline.pipeline import DataProcessingPipeline
from .models import (
    DataIngestionRequest,
    DataIngestionResponse,
    PipelineExecutionRequest,
    PipelineExecutionResponse,
    DataCleaningResponse,
    DatabaseStatsResponse,
    PipelineStatusResponse,
    HealthCheckResponse,
    CSVProcessingRequest,
    CSVProcessingResponse,
    CSVValidationRequest,
    CSVValidationResponse,
    CSVInfoResponse,
    RawDataProcessingRequest,
    RawDataProcessingResponse
)


logger = logging.getLogger(__name__)


class APIService:
    """Main API service that orchestrates all data processing operations."""
    
    def __init__(self):
        self.settings = get_settings()
        self.neo4j_config = None
        self.processing_config = None
        self.graph_manager = None
        self.ingestion_service = None
        self.merging_service = None
        self.pipeline = None
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all services and connections."""
        try:
            logger.info("Initializing API Service...")
            
            # Initialize configurations
            self.neo4j_config = Neo4jConfig(
                uri=self.settings.neo4j_uri,
                username=self.settings.neo4j_username,
                password=self.settings.neo4j_password
            )
            
            self.processing_config = ProcessingConfig()
            
            # Initialize graph manager
            self.graph_manager = GraphDataIngest(self.neo4j_config, self.processing_config)
            await self.graph_manager.connect()
            logger.info("✓ Connected to Neo4j")
            
            # Initialize services
            self.ingestion_service = DataIngestionService()
            self.merging_service = UserMergingService(
                graph_manager=self.graph_manager,
                confidence_threshold=self.settings.merge_confidence_threshold
            )
            
            # Initialize pipeline
            self.pipeline = DataProcessingPipeline(
                ingestion_service=self.ingestion_service,
                merging_service=self.merging_service,
                graph_manager=self.graph_manager
            )
            
            self.initialized = True
            logger.info("✓ API Service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize API Service: {e}")
            self.initialized = False
            return False
    
    async def cleanup(self):
        """Clean up resources."""
        if self.graph_manager:
            await self.graph_manager.disconnect()
            logger.info("✓ Neo4j connection closed")
    
    async def ingest_data(self, request: DataIngestionRequest) -> DataIngestionResponse:
        """Ingest data from CSV files."""
        if not self.initialized:
            return DataIngestionResponse(
                success=False,
                accounts_created=0,
                emails_created=0,
                relationships_created=0,
                message="API Service not initialized",
                errors=["Service not initialized"]
            )
        
        try:
            logger.info("Starting data ingestion...")
            
            # Clean data if requested
            if request.clean_before_ingestion:
                logger.info("Cleaning existing data before ingestion...")
                cleaning_result = await self.graph_manager.clean_all_data()
                logger.info(f"Data cleaning completed: {cleaning_result['message']}")
            
            # Ingest data
            ingestion_result = await self.graph_manager.ingest_data_from_csv(
                account_email_mapping_file=request.account_email_mapping_file,
                account_properties_file=request.account_properties_file
            )
            
            return DataIngestionResponse(
                success=ingestion_result['success'],
                accounts_created=ingestion_result['accounts_created'],
                emails_created=ingestion_result['emails_created'],
                relationships_created=ingestion_result['relationships_created'],
                message=ingestion_result['message']
            )
            
        except Exception as e:
            logger.error(f"Data ingestion failed: {e}")
            return DataIngestionResponse(
                success=False,
                accounts_created=0,
                emails_created=0,
                relationships_created=0,
                message=f"Data ingestion failed: {str(e)}",
                errors=[str(e)]
            )
    
    async def execute_pipeline(self, request: PipelineExecutionRequest) -> PipelineExecutionResponse:
        """Execute the data processing pipeline."""
        if not self.initialized:
            return PipelineExecutionResponse(
                success=False,
                source_id=request.source_id,
                accounts_processed=0,
                users_created=0,
                message="API Service not initialized",
                errors=["Service not initialized"]
            )
        
        try:
            logger.info(f"Executing pipeline for source: {request.source_id}")
            
            if request.preview_only:
                # Run preview mode
                preview_result = await self.pipeline.preview_pipeline(request.source_id)
                
                return PipelineExecutionResponse(
                    success=True,
                    source_id=request.source_id,
                    accounts_processed=preview_result.get('accounts_processed', 0),
                    users_created=0,
                    message=preview_result.get('message', 'Preview completed'),
                    preview_data=preview_result.get('preview_data')
                )
            else:
                # Run full pipeline
                result = await self.pipeline.process_data_source(request.source_id)
                
                return PipelineExecutionResponse(
                    success=result.success,
                    source_id=request.source_id,
                    accounts_processed=result.accounts_processed,
                    users_created=result.users_created,
                    message=result.message,
                    errors=result.errors
                )
                
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            return PipelineExecutionResponse(
                success=False,
                source_id=request.source_id,
                accounts_processed=0,
                users_created=0,
                message=f"Pipeline execution failed: {str(e)}",
                errors=[str(e)]
            )
    
    async def clean_data(self) -> DataCleaningResponse:
        """Clean all data from the database."""
        if not self.initialized:
            return DataCleaningResponse(
                success=False,
                nodes_deleted=0,
                relationships_deleted=0,
                remaining_nodes=0,
                remaining_relationships=0,
                message="API Service not initialized"
            )
        
        try:
            logger.info("Cleaning all data from database...")
            result = await self.graph_manager.clean_all_data()
            
            return DataCleaningResponse(
                success=result['success'],
                nodes_deleted=result['nodes_deleted'],
                relationships_deleted=result['relationships_deleted'],
                remaining_nodes=result['remaining_nodes'],
                remaining_relationships=result['remaining_relationships'],
                message=result['message']
            )
            
        except Exception as e:
            logger.error(f"Data cleaning failed: {e}")
            return DataCleaningResponse(
                success=False,
                nodes_deleted=0,
                relationships_deleted=0,
                remaining_nodes=0,
                remaining_relationships=0,
                message=f"Data cleaning failed: {str(e)}"
            )
    
    async def get_database_stats(self) -> DatabaseStatsResponse:
        """Get database statistics."""
        if not self.initialized:
            return DatabaseStatsResponse(
                account_count=0,
                email_count=0,
                relationship_count=0,
                sample_accounts=[],
                timestamp=None
            )
        
        try:
            stats = await self.graph_manager.get_ingestion_statistics()
            
            return DatabaseStatsResponse(
                account_count=stats['account_count'],
                email_count=stats['email_count'],
                relationship_count=stats['relationship_count'],
                sample_accounts=stats['sample_accounts']
            )
            
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return DatabaseStatsResponse(
                account_count=0,
                email_count=0,
                relationship_count=0,
                sample_accounts=[],
                timestamp=None
            )
    
    async def get_pipeline_status(self) -> PipelineStatusResponse:
        """Get pipeline status."""
        if not self.initialized:
            return PipelineStatusResponse(
                status="not_initialized",
                database_connected=False,
                configured_sources=0,
                sources=[]
            )
        
        try:
            status = await self.pipeline.get_pipeline_status()
            
            return PipelineStatusResponse(
                status=status['status'],
                database_connected=status['database_connected'],
                configured_sources=status['configured_sources'],
                sources=status['sources']
            )
            
        except Exception as e:
            logger.error(f"Failed to get pipeline status: {e}")
            return PipelineStatusResponse(
                status="error",
                database_connected=False,
                configured_sources=0,
                sources=[],
                error=str(e)
            )
    
    async def health_check(self) -> HealthCheckResponse:
        """Perform health check."""
        try:
            db_connected = False
            if self.initialized and self.graph_manager:
                db_connected = await self.graph_manager.test_connection()
            
            return HealthCheckResponse(
                status="healthy" if self.initialized and db_connected else "unhealthy",
                version=self.settings.app_version,
                database_connected=db_connected
            )
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return HealthCheckResponse(
                status="error",
                version=self.settings.app_version,
                database_connected=False
            )
    
    async def process_csv(self, request: CSVProcessingRequest) -> CSVProcessingResponse:
        """Process a CSV file and return account data."""
        try:
            logger.info(f"Processing CSV file: {request.file_path}")
            
            # Initialize CSV processor
            processor = CSVDataProcessor(source_id=request.source_id)
            
            # Process the file
            accounts = await processor.process_file(
                file_path=request.file_path,
                columns=request.columns
            )
            
            # Prepare sample accounts for response
            sample_accounts = []
            for account in accounts[:3]:  # First 3 accounts as samples
                sample_accounts.append({
                    "account_id": account.account_id,
                    "email": account.email,
                    "deposit_amount_sum": account.deposit_amount_sum,
                    "reg_time": str(account.reg_time) if account.reg_time else None
                })
            
            return CSVProcessingResponse(
                success=True,
                accounts_processed=len(accounts),
                errors_count=0,  # CSV processor doesn't return error count, would need to modify it
                errors=[],
                message=f"Successfully processed {len(accounts)} accounts from {request.file_path}",
                sample_accounts=sample_accounts
            )
            
        except Exception as e:
            logger.error(f"CSV processing failed: {e}")
            return CSVProcessingResponse(
                success=False,
                accounts_processed=0,
                errors_count=1,
                errors=[str(e)],
                message=f"CSV processing failed: {str(e)}"
            )
    
    async def validate_csv(self, request: CSVValidationRequest) -> CSVValidationResponse:
        """Validate a CSV file."""
        try:
            logger.info(f"Validating CSV file: {request.file_path}")
            
            from pathlib import Path
            import csv
            
            file_path = Path(request.file_path)
            if not file_path.exists():
                return CSVValidationResponse(
                    success=False,
                    is_valid=False,
                    total_rows=0,
                    valid_rows=0,
                    invalid_rows=0,
                    validation_errors=[f"File not found: {request.file_path}"],
                    available_columns=[],
                    message=f"File not found: {request.file_path}"
                )
            
            # Read CSV to get columns and count rows
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                available_columns = reader.fieldnames or []
                total_rows = sum(1 for _ in reader)
            
            # Check if required columns exist
            validation_errors = []
            if request.columns:
                missing_columns = [col for col in request.columns if col not in available_columns]
                if missing_columns:
                    validation_errors.append(f"Missing columns: {missing_columns}")
            
            # Basic validation (file exists, has headers, has data)
            if total_rows == 0:
                validation_errors.append("CSV file is empty (no data rows)")
            
            if not available_columns:
                validation_errors.append("CSV file has no headers")
            
            is_valid = len(validation_errors) == 0
            
            return CSVValidationResponse(
                success=True,
                is_valid=is_valid,
                total_rows=total_rows,
                valid_rows=total_rows if is_valid else 0,
                invalid_rows=0 if is_valid else total_rows,
                validation_errors=validation_errors,
                available_columns=available_columns,
                message=f"CSV validation {'passed' if is_valid else 'failed'}. {total_rows} rows found."
            )
            
        except Exception as e:
            logger.error(f"CSV validation failed: {e}")
            return CSVValidationResponse(
                success=False,
                is_valid=False,
                total_rows=0,
                valid_rows=0,
                invalid_rows=0,
                validation_errors=[str(e)],
                available_columns=[],
                message=f"CSV validation failed: {str(e)}"
            )
    
    async def get_csv_info(self, file_path: str) -> CSVInfoResponse:
        """Get information about a CSV file."""
        try:
            logger.info(f"Getting CSV info for: {file_path}")
            
            from pathlib import Path
            import csv
            
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Get file size
            file_size = file_path_obj.stat().st_size
            
            # Read CSV to get columns and sample data
            with open(file_path_obj, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                columns = reader.fieldnames or []
                
                # Get sample data (first 3 rows)
                sample_data = []
                for i, row in enumerate(reader):
                    if i >= 3:  # Only first 3 rows
                        break
                    sample_data.append(dict(row))
                
                # Count total rows (including the ones we already read)
                total_rows = len(sample_data)
                # Read remaining rows to get total count
                for _ in reader:
                    total_rows += 1
            
            return CSVInfoResponse(
                file_path=file_path,
                file_size=file_size,
                total_rows=total_rows,
                columns=columns,
                sample_data=sample_data
            )
            
        except Exception as e:
            logger.error(f"Failed to get CSV info: {e}")
            raise
    
    async def process_raw_data(self, request: RawDataProcessingRequest) -> RawDataProcessingResponse:
        """Process all CSV files from raw_data directory and save to processed_data."""
        try:
            logger.info("Processing raw data from raw_data directory...")
            
            # Initialize CSV processor with default source_id
            processor = CSVDataProcessor(source_id="raw_data_processed")
            
            # Process all files from raw_data directory and output to two CSV files
            output_files = processor.process_raw_data_to_csv(
                columns=request.columns,
                chunk_size=request.chunk_size
            )
            
            # Get file counts for response
            from pathlib import Path
            raw_data_dir = Path("raw_data")
            csv_files = list(raw_data_dir.glob("*_results.csv"))
            files_processed = len(csv_files)
            
            # Count accounts in the output files
            accounts_processed = 0
            sample_accounts = []
            
            try:
                import pandas as pd
                account_properties_df = pd.read_csv(output_files["account_properties"])
                accounts_processed = len(account_properties_df)
                
                # Prepare sample accounts for response
                if accounts_processed > 0:
                    # Get first 3 accounts as samples
                    sample_data = account_properties_df.head(3)
                    for _, row in sample_data.iterrows():
                        sample_accounts.append({
                            "account_id": row['account_id'],
                            "email": row.get('email', ''),
                            "deposit_amount_sum": row.get('deposit_amount_sum', ''),
                            "reg_time": str(row.get('reg_time', '')) if pd.notna(row.get('reg_time')) else None
                        })
            except Exception as e:
                logger.warning(f"Could not read output files for statistics: {e}")
            
            return RawDataProcessingResponse(
                success=True,
                files_processed=files_processed,
                accounts_processed=accounts_processed,
                errors_count=0,
                errors=[],
                output_files=output_files,
                message=f"Successfully processed {accounts_processed} accounts from {files_processed} files. Output saved to processed_data/",
                sample_accounts=sample_accounts
            )
            
        except Exception as e:
            logger.error(f"Raw data processing failed: {e}")
            return RawDataProcessingResponse(
                success=False,
                files_processed=0,
                accounts_processed=0,
                errors_count=1,
                errors=[str(e)],
                output_files={},
                message=f"Raw data processing failed: {str(e)}"
            ) 