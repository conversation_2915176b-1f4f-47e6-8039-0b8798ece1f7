"""
Data processing utilities for the One ID Mapping System.
"""

import pandas as pd
import logging
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
import os
import glob

logger = logging.getLogger(__name__)


def process_raw_data_to_csv(
    columns: Optional[List[str]] = None,
    output_dir: str = "processed_data",
    chunk_size: int = 100_000
) -> Dict[str, str]:
    """
    Process all CSV files from raw_data directory and output two CSV files:
    1. account_email_mapping.csv - contains account_id and email
    2. account_properties.csv - contains account_id and other properties
    
    Args:
        columns: List of columns to extract from raw data
        output_dir: Directory to save processed CSV files
        chunk_size: Number of rows to process at once for large files
    
    Returns:
        Dictionary with output file paths
    """
    try:
        # Ensure directories exist
        raw_data_dir = Path("raw_data")
        processed_data_dir = Path(output_dir)
        
        raw_data_dir.mkdir(exist_ok=True)
        processed_data_dir.mkdir(exist_ok=True)
        
        # Prepare output file paths
        mapping_path = processed_data_dir / "account_email_mapping.csv"
        properties_path = processed_data_dir / "account_properties.csv"
        
        # Find all CSV files in raw_data (look for *_results.csv pattern)
        csv_files = list(raw_data_dir.glob("*_results.csv"))
        if not csv_files:
            logger.warning("No *_results.csv files found in raw_data directory")
            return {
                "account_email_mapping": str(mapping_path),
                "account_properties": str(properties_path)
            }
        
        logger.info(f"Found {len(csv_files)} CSV files to process")
        
        # Collect all data first, then write once
        all_mapping_data = []
        all_properties_data = []
        
        total_accounts = 0
        
        for csv_file in csv_files:
            # Extract project_id from filename (e.g., "46_results.csv" -> "46")
            project_id = csv_file.stem.split('_')[0]
            logger.info(f"Processing project {project_id} from {csv_file.name}")
            
            try:
                # Read in chunks for large files
                for chunk_num, chunk in enumerate(pd.read_csv(csv_file, chunksize=chunk_size)):
                    # Filter columns if specified
                    if columns:
                        available_columns = [col for col in columns if col in chunk.columns]
                        if not available_columns:
                            logger.warning(f"  No requested columns found in {csv_file.name}, skipping")
                            continue
                        chunk = chunk[available_columns]
                    
                    # Generate new account_id with project prefix
                    if 'account_id' in chunk.columns:
                        chunk['account_id'] = chunk['account_id'].astype(str).apply(lambda x: f"{project_id}_{x}")
                    else:
                        logger.warning(f"  No account_id column found in {csv_file.name}")
                        continue
                    
                    # Prepare mapping and properties DataFrames
                    if 'email' in chunk.columns:
                        mapping_df = chunk[['account_id', 'email']]
                        # Remove rows where email is null or empty
                        mapping_df = mapping_df.dropna(subset=['email'])
                        mapping_df = mapping_df[mapping_df['email'].str.strip() != '']
                    else:
                        # If no email column, create empty dataframe with account_id
                        mapping_df = chunk[['account_id']].copy()
                        mapping_df['email'] = None
                    
                    # Create properties dataframe (all columns except email)
                    properties_cols = [col for col in chunk.columns if col != 'email']
                    properties_df = chunk[properties_cols]
                    
                    # Collect data instead of writing immediately
                    all_mapping_data.append(mapping_df)
                    all_properties_data.append(properties_df)
                    
                    total_accounts += len(properties_df)
                
                logger.info(f"✓ Completed processing {csv_file.name}")
                
            except Exception as e:
                logger.error(f"Failed to process {csv_file}: {e}")
        
        # Write all data at once (overwrite mode)
        if all_mapping_data:
            final_mapping_df = pd.concat(all_mapping_data, ignore_index=True)
            final_properties_df = pd.concat(all_properties_data, ignore_index=True)
            
            final_mapping_df.to_csv(mapping_path, index=False)
            final_properties_df.to_csv(properties_path, index=False)
        
        logger.info(f"✓ Processing complete. Total accounts: {total_accounts}")
        logger.info(f"✓ Output files:\n  - {mapping_path}\n  - {properties_path}")
        
        return {
            "account_email_mapping": str(mapping_path),
            "account_properties": str(properties_path)
        }
        
    except Exception as e:
        logger.error(f"Error processing raw data: {str(e)}")
        raise


def get_processed_file_paths(output_dir: str = "processed_data") -> Dict[str, str]:
    """
    Get the file paths for the processed CSV files.
    
    Args:
        output_dir: Directory containing processed CSV files
    
    Returns:
        Dictionary with file paths for account_email_mapping and account_properties
    """
    return {
        "account_email_mapping": os.path.join(output_dir, "account_email_mapping.csv"),
        "account_properties": os.path.join(output_dir, "account_properties.csv")
    }


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Generate summary statistics for the processed data.
    
    Args:
        df: Processed DataFrame
    
    Returns:
        Dictionary containing summary statistics
    """
    summary = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'columns': list(df.columns),
        'missing_values': df.isnull().sum().to_dict(),
        'data_types': df.dtypes.to_dict()
    }
    
    # Add numeric column statistics
    numeric_cols = df.select_dtypes(include=['number']).columns
    if len(numeric_cols) > 0:
        summary['numeric_stats'] = df[numeric_cols].describe().to_dict()
    
    # Add email domain analysis if email column exists
    if 'email' in df.columns:
        email_domains = df['email'].str.split('@').str[1].value_counts()
        summary['top_email_domains'] = email_domains.head(10).to_dict()
    
    return summary


def validate_data(df: pd.DataFrame) -> List[str]:
    """
    Validate DataFrame for processing.
    
    Args:
        df: DataFrame to validate
    
    Returns:
        List of validation warnings/errors
    """
    issues = []
    
    # Check for required columns
    required_columns = ['account_id']
    for col in required_columns:
        if col not in df.columns:
            issues.append(f"Missing required column: {col}")
    
    # Check for unique account_ids
    if 'account_id' in df.columns:
        duplicate_ids = df['account_id'].duplicated().sum()
        if duplicate_ids > 0:
            issues.append(f"Found {duplicate_ids} duplicate account_ids")
    
    # Check for null values in critical columns
    critical_columns = ['account_id']
    for col in critical_columns:
        if col in df.columns:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                issues.append(f"Found {null_count} null values in {col}")
    
    return issues 