"""
API models for the One ID Mapping System.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .user_data import UserProfile


class UserProfileResponse(BaseModel):
    """API response for user profile requests."""
    
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class UserByAccountResponse(BaseModel):
    """API response for user lookup by account."""
    
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class MergingRequest(BaseModel):
    """API request for executing user merging."""
    
    source_id: str = Field(..., description="ID of the data source to process")
    force_merge: bool = Field(default=False, description="Force merge even with low confidence")
    dry_run: bool = Field(default=False, description="Run merging without committing changes")


class MergingResponse(BaseModel):
    """API response for merging operations."""
    
    success: bool
    users_created: int = 0
    accounts_merged: int = 0
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Optional[Dict[str, Any]] = None


class PipelineResult(BaseModel):
    """Result of data processing pipeline execution."""
    
    success: bool
    accounts_processed: int = 0
    users_created: int = 0
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)


class ErrorResponse(BaseModel):
    """Standard error response format."""
    
    error: str
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class HealthCheckResponse(BaseModel):
    """Health check response."""
    
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    services: Dict[str, str] = Field(default_factory=dict) 