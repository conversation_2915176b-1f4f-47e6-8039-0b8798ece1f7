"""
Account data models for the One ID Mapping System.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import logging


@dataclass
class AccountData:
    """Represents account data from various sources."""
    
    account_id: str
    email: Optional[str] = None
    deposit_amount_sum: Optional[float] = None
    deposit_times_sum: Optional[int] = None
    bet_money_sum: Optional[float] = None
    withdraw_amount_sum: Optional[float] = None
    active_time: Optional[datetime] = None
    reg_time: Optional[datetime] = None
    current_money: Optional[float] = None
    update_time: Optional[datetime] = None
    source_id: str = ""
    
    def __post_init__(self):
        """Validate account data after initialization."""
        if not self.account_id:
            raise ValueError("account_id is required")
        
        # Validate numeric fields
        if self.deposit_amount_sum is not None and self.deposit_amount_sum < 0:
            raise ValueError("deposit_amount_sum cannot be negative")
        
        if self.bet_money_sum is not None and self.bet_money_sum < 0:
            raise ValueError("bet_money_sum cannot be negative")
        
        if self.withdraw_amount_sum is not None and self.withdraw_amount_sum < 0:
            raise ValueError("withdraw_amount_sum cannot be negative")
        
        if self.current_money is not None and self.current_money < 0:
            raise ValueError("current_money cannot be negative")
        
        # Validate time relationships
        # if self.reg_time and self.active_time:
        #     if self.reg_time > self.active_time:
        #         # Log warning instead of raising error for data quality issues
        #         logger = logging.getLogger(__name__)
        #         logger.warning(f"Account {self.account_id}: registration time ({self.reg_time}) is after active time ({self.active_time})")
        #         # Optionally, you could swap the times or set active_time to reg_time
        #         # self.active_time = self.reg_time
    
    def to_dict(self) -> dict:
        """Convert to dictionary for database operations."""
        return {
            "account_id": self.account_id,
            "email": self.email,
            "deposit_amount_sum": self.deposit_amount_sum,
            "deposit_times_sum": self.deposit_times_sum,
            "bet_money_sum": self.bet_money_sum,
            "withdraw_amount_sum": self.withdraw_amount_sum,
            "active_time": self.active_time,
            "reg_time": self.reg_time,
            "current_money": self.current_money,
            "update_time": self.update_time,
            "source_id": self.source_id
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "AccountData":
        """Create AccountData from dictionary."""
        return cls(**data) 