"""
Merge data models for the One ID Mapping System.
"""

from dataclasses import dataclass
from typing import List

from .account_data import AccountData


@dataclass
class MergeGroup:
    """Represents a group of accounts to be merged."""
    
    accounts: List[AccountData]
    confidence_score: float
    merge_criteria: str
    merge_id: str
    
    def __post_init__(self):
        """Validate merge group after initialization."""
        if not self.accounts:
            raise ValueError("accounts list cannot be empty")
        
        if len(self.accounts) < 2:
            raise ValueError("merge group must contain at least 2 accounts")
        
        if not (0.0 <= self.confidence_score <= 1.0):
            raise ValueError("confidence_score must be between 0.0 and 1.0")
        
        if not self.merge_criteria:
            raise ValueError("merge_criteria is required")
        
        if not self.merge_id:
            raise ValueError("merge_id is required")
    
    def get_account_ids(self) -> List[str]:
        """Get list of account IDs in this merge group."""
        return [acc.account_id for acc in self.accounts]
    
    def get_emails(self) -> List[str]:
        """Get list of unique emails in this merge group."""
        emails = set()
        for acc in self.accounts:
            if acc.email:
                emails.add(acc.email.lower().strip())
        return list(emails)
    
    def to_dict(self) -> dict:
        """Convert to dictionary for logging and serialization."""
        return {
            "merge_id": self.merge_id,
            "confidence_score": self.confidence_score,
            "merge_criteria": self.merge_criteria,
            "account_count": len(self.accounts),
            "account_ids": self.get_account_ids(),
            "emails": self.get_emails()
        }


@dataclass
class MergeResult:
    """Result of a merge operation."""
    
    success: bool
    user_id: str = ""
    accounts_merged: int = 0
    merge_id: str = ""
    error_message: str = ""
    
    def __post_init__(self):
        """Validate merge result after initialization."""
        if self.success:
            if not self.user_id:
                raise ValueError("user_id is required for successful merge")
            if self.accounts_merged <= 0:
                raise ValueError("accounts_merged must be positive for successful merge")
        else:
            if not self.error_message:
                raise ValueError("error_message is required for failed merge")
    
    def to_dict(self) -> dict:
        """Convert to dictionary for API responses."""
        return {
            "success": self.success,
            "user_id": self.user_id,
            "accounts_merged": self.accounts_merged,
            "merge_id": self.merge_id,
            "error_message": self.error_message
        } 