"""
User data models for the One ID Mapping System.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from .account_data import AccountData


@dataclass
class UserData:
    """Represents aggregated user data."""
    
    user_id: str
    total_deposits: float = 0.0
    total_bets: float = 0.0
    earliest_reg_time: Optional[datetime] = None
    latest_activity: Optional[datetime] = None
    account_count: int = 0
    email_count: int = 0
    
    def __post_init__(self):
        """Validate user data after initialization."""
        if not self.user_id:
            raise ValueError("user_id is required")
        
        if self.total_deposits < 0:
            raise ValueError("total_deposits cannot be negative")
        
        if self.total_bets < 0:
            raise ValueError("total_bets cannot be negative")
        
        if self.account_count < 0:
            raise ValueError("account_count cannot be negative")
        
        if self.email_count < 0:
            raise ValueError("email_count cannot be negative")
    
    def to_dict(self) -> dict:
        """Convert to dictionary for database operations."""
        return {
            "user_id": self.user_id,
            "total_deposits": self.total_deposits,
            "total_bets": self.total_bets,
            "earliest_reg_time": self.earliest_reg_time,
            "latest_activity": self.latest_activity,
            "account_count": self.account_count,
            "email_count": self.email_count
        }


@dataclass
class UserProfile:
    """Complete user profile with all related data."""
    
    user_id: str
    attributes: UserData
    accounts: List[AccountData]
    emails: List[str]
    created_at: datetime
    updated_at: datetime
    
    def __post_init__(self):
        """Validate user profile after initialization."""
        if not self.user_id:
            raise ValueError("user_id is required")
        
        if not self.attributes:
            raise ValueError("attributes are required")
        
        if self.attributes.user_id != self.user_id:
            raise ValueError("user_id mismatch between profile and attributes")
    
    def to_dict(self) -> dict:
        """Convert to dictionary for API responses."""
        return {
            "user_id": self.user_id,
            "attributes": self.attributes.to_dict(),
            "accounts": [acc.to_dict() for acc in self.accounts],
            "emails": self.emails,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }


@dataclass
class UserInsights:
    """User insights and analytics data."""
    
    total_accounts: int
    total_deposits: float
    total_bets: float
    earliest_registration: Optional[datetime] = None
    latest_activity: Optional[datetime] = None
    average_deposit: float = 0.0
    net_deposits: float = 0.0
    activity_frequency: float = 0.0
    
    def __post_init__(self):
        """Validate user insights after initialization."""
        if self.total_accounts < 0:
            raise ValueError("total_accounts cannot be negative")
        
        if self.total_deposits < 0:
            raise ValueError("total_deposits cannot be negative")
        
        if self.total_bets < 0:
            raise ValueError("total_bets cannot be negative")
    
    def to_dict(self) -> dict:
        """Convert to dictionary for API responses."""
        return {
            "total_accounts": self.total_accounts,
            "total_deposits": self.total_deposits,
            "total_bets": self.total_bets,
            "earliest_registration": self.earliest_registration,
            "latest_activity": self.latest_activity,
            "average_deposit": self.average_deposit,
            "net_deposits": self.net_deposits,
            "activity_frequency": self.activity_frequency
        } 