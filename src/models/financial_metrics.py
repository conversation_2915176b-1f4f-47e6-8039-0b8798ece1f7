"""
Financial metrics models for the One ID Mapping System.
"""

from dataclasses import dataclass


@dataclass
class FinancialMetrics:
    """Aggregated financial metrics for a user."""
    
    total_deposits: float
    total_bets: float
    total_withdrawals: float
    total_deposit_times: int
    current_money: float
    net_deposits: float
    average_deposit: float
    
    def __post_init__(self):
        """Validate financial metrics after initialization."""
        if self.total_deposits < 0:
            raise ValueError("total_deposits cannot be negative")
        
        if self.total_bets < 0:
            raise ValueError("total_bets cannot be negative")
        
        if self.total_withdrawals < 0:
            raise ValueError("total_withdrawals cannot be negative")
        
        if self.total_deposit_times < 0:
            raise ValueError("total_deposit_times cannot be negative")
        
        if self.current_money < 0:
            raise ValueError("current_money cannot be negative")
        
        # Validate calculated fields
        expected_net_deposits = self.total_deposits - self.total_withdrawals
        if abs(self.net_deposits - expected_net_deposits) > 0.01:  # Allow small floating point differences
            raise ValueError(f"net_deposits should be {expected_net_deposits}, got {self.net_deposits}")
        
        if self.total_deposit_times > 0:
            expected_average = self.total_deposits / self.total_deposit_times
            if abs(self.average_deposit - expected_average) > 0.01:
                raise ValueError(f"average_deposit should be {expected_average}, got {self.average_deposit}")
        elif self.average_deposit != 0:
            raise ValueError("average_deposit should be 0 when total_deposit_times is 0")
    
    def to_dict(self) -> dict:
        """Convert to dictionary for API responses."""
        return {
            "total_deposits": self.total_deposits,
            "total_bets": self.total_bets,
            "total_withdrawals": self.total_withdrawals,
            "total_deposit_times": self.total_deposit_times,
            "current_money": self.current_money,
            "net_deposits": self.net_deposits,
            "average_deposit": self.average_deposit
        }
    
    @classmethod
    def calculate_from_accounts(cls, accounts) -> "FinancialMetrics":
        """Calculate financial metrics from a list of accounts."""
        total_deposits = sum(acc.deposit_amount_sum or 0 for acc in accounts)
        total_bets = sum(acc.bet_money_sum or 0 for acc in accounts)
        total_withdrawals = sum(acc.withdraw_amount_sum or 0 for acc in accounts)
        total_deposit_times = sum(acc.deposit_times_sum or 0 for acc in accounts)
        
        # Current money: use the latest value
        current_money_values = [acc.current_money for acc in accounts if acc.current_money is not None]
        current_money = max(current_money_values) if current_money_values else 0.0
        
        # Calculate derived metrics
        net_deposits = total_deposits - total_withdrawals
        average_deposit = total_deposits / total_deposit_times if total_deposit_times > 0 else 0.0
        
        return cls(
            total_deposits=total_deposits,
            total_bets=total_bets,
            total_withdrawals=total_withdrawals,
            total_deposit_times=total_deposit_times,
            current_money=current_money,
            net_deposits=net_deposits,
            average_deposit=average_deposit
        ) 