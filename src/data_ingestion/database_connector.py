"""
Database connector for the One ID Mapping System.
"""

import asyncio
import logging
from typing import Dict, List, Optional

import asyncpg
import aiomysql

from ..models.account_data import AccountData
from ..config.database_config import PostgreSQLConfig


logger = logging.getLogger(__name__)


class DatabaseConnectionError(Exception):
    """Raised when database connection fails."""
    pass


class DatabaseConnector:
    """Connects to various databases and fetches account data."""
    
    def __init__(self, config: PostgreSQLConfig):
        self.config = config
        self.connection_pool = None
        self._available_columns = [
            'account_id', 'email', 'deposit_amount_sum', 'deposit_times_sum',
            'bet_money_sum', 'withdraw_amount_sum', 'active_time', 'reg_time',
            'current_money', 'update_time', 'source_id'
        ]
    
    @property
    def available_columns(self) -> List[str]:
        """Get list of available columns in the database."""
        return self._available_columns.copy()
    
    def set_available_columns(self, columns: List[str]):
        """Set the list of available columns."""
        self._available_columns = columns.copy()
        logger.info(f"Updated available columns: {self._available_columns}")
    
    async def connect(self) -> bool:
        """Establish database connection."""
        try:
            if self.config:
                self.connection_pool = await asyncpg.create_pool(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    min_size=self.config.min_connections,
                    max_size=self.config.max_connections,
                    command_timeout=self.config.connection_timeout
                )
                logger.info(f"Connected to PostgreSQL database: {self.config.database}")
                return True
            else:
                raise DatabaseConnectionError("No database configuration provided")
                
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise DatabaseConnectionError(f"Failed to connect: {e}")
    
    async def disconnect(self):
        """Close database connection."""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("Database connection closed")
    
    def _build_select_query(self, columns: Optional[List[str]] = None) -> str:
        """Build SELECT query with specified columns."""
        if columns is None:
            columns = self._available_columns
        
        # Filter out columns that don't exist in available columns
        valid_columns = [col for col in columns if col in self._available_columns]
        if not valid_columns:
            valid_columns = ['account_id']  # Always include account_id as fallback
        
        column_list = ', '.join(valid_columns)
        return f"SELECT {column_list}"
    
    async def fetch_accounts(self, query: str, params: Optional[Dict] = None) -> List[AccountData]:
        """Fetch account data from database."""
        if not self.connection_pool:
            raise DatabaseConnectionError("Database not connected")
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, **(params or {}))
                return [self._row_to_account(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error fetching accounts: {e}")
            raise DatabaseConnectionError(f"Failed to fetch accounts: {e}")
    
    async def fetch_accounts_by_source(self, source_id: str, columns: Optional[List[str]] = None) -> List[AccountData]:
        """Fetch accounts for a specific source with specified columns."""
        select_clause = self._build_select_query(columns)
        query = f"""
        {select_clause}
        FROM accounts 
        WHERE source_id = $1
        """
        
        return await self.fetch_accounts(query, {"source_id": source_id})
    
    async def fetch_accounts_by_date_range(self, start_date: str, end_date: str, 
                                         source_id: Optional[str] = None, 
                                         columns: Optional[List[str]] = None) -> List[AccountData]:
        """Fetch accounts within a date range with specified columns."""
        select_clause = self._build_select_query(columns)
        query = f"""
        {select_clause}
        FROM accounts 
        WHERE update_time BETWEEN $1 AND $2
        """
        
        params = {"start_date": start_date, "end_date": end_date}
        
        if source_id:
            query += " AND source_id = $3"
            params["source_id"] = source_id
        
        return await self.fetch_accounts(query, params)
    
    async def fetch_accounts_with_columns(self, columns: List[str], 
                                        where_clause: Optional[str] = None,
                                        params: Optional[Dict] = None) -> List[AccountData]:
        """Fetch accounts with specific columns and optional where clause."""
        select_clause = self._build_select_query(columns)
        query = f"{select_clause} FROM accounts"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        return await self.fetch_accounts(query, params)
    
    def _row_to_account(self, row) -> AccountData:
        """Convert database row to AccountData object."""
        return AccountData(
            account_id=row['account_id'],
            email=row.get('email'),
            deposit_amount_sum=float(row['deposit_amount_sum']) if row.get('deposit_amount_sum') else None,
            deposit_times_sum=int(row['deposit_times_sum']) if row.get('deposit_times_sum') else None,
            bet_money_sum=float(row['bet_money_sum']) if row.get('bet_money_sum') else None,
            withdraw_amount_sum=float(row['withdraw_amount_sum']) if row.get('withdraw_amount_sum') else None,
            active_time=row.get('active_time'),
            reg_time=row.get('reg_time'),
            current_money=float(row['current_money']) if row.get('current_money') else None,
            update_time=row.get('update_time'),
            source_id=row.get('source_id', '')
        )
    
    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    async def get_table_info(self, table_name: str) -> Dict:
        """Get table schema information."""
        if not self.connection_pool:
            raise DatabaseConnectionError("Database not connected")
        
        try:
            async with self.connection_pool.acquire() as conn:
                query = """
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = $1
                ORDER BY ordinal_position
                """
                rows = await conn.fetch(query, table_name)
                
                return {
                    "table_name": table_name,
                    "columns": [dict(row) for row in rows]
                }
                
        except Exception as e:
            logger.error(f"Error getting table info: {e}")
            raise DatabaseConnectionError(f"Failed to get table info: {e}") 