"""
Main data ingestion service for the One ID Mapping System.
"""

import logging
from typing import Dict, List, Optional

from ..models.account_data import AccountData
from .csv_processor import CSVDataProcessor
from .database_connector import DatabaseConnector
from .data_validator import DataValidator


logger = logging.getLogger(__name__)


class DataIngestionService:
    """Main service for ingesting data from various sources."""
    
    def __init__(self):
        self.csv_processor = None
        self.db_connector = None
        self.validator = DataValidator()
        self._source_configs = {}
        self._default_columns = [
            'account_id', 'email', 'deposit_amount_sum', 'deposit_times_sum',
            'bet_money_sum', 'withdraw_amount_sum', 'active_time', 'reg_time',
            'current_money', 'update_time', 'source_id'
        ]
    
    @property
    def default_columns(self) -> List[str]:
        """Get default columns for data processing."""
        return self._default_columns.copy()
    
    def set_default_columns(self, columns: List[str]):
        """Set default columns for data processing."""
        self._default_columns = columns.copy()
        logger.info(f"Updated default columns: {self._default_columns}")
    
    async def process_source(self, source_id: str, source_type: str = "csv", 
                           columns: Optional[List[str]] = None, **kwargs) -> List[AccountData]:
        """Process data from a specific source with specified columns."""
        try:
            config = self._source_configs.get(source_id)
            if not config:
                raise ValueError(f"Source {source_id} not configured")
            
            # Use provided columns or default columns
            if columns is None:
                columns = self._default_columns
            
            if source_type.lower() == "csv":
                file_path = kwargs.get("file_path") or config.get("file_path")
                if not file_path:
                    raise ValueError(f"No file_path configured for source {source_id}")
                return await self._process_csv_source(source_id, file_path, columns)
            elif source_type.lower() == "database":
                return await self._process_database_source(source_id, columns, **kwargs)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")
                
        except Exception as e:
            logger.error(f"Error processing source {source_id}: {e}")
            raise
    
    async def _process_csv_source(self, source_id: str, file_path: str, 
                                columns: Optional[List[str]] = None) -> List[AccountData]:
        """Process CSV file source with specified columns."""
        logger.info(f"Processing CSV source {source_id} from {file_path}")
        logger.info(f"Using columns: {columns}")
        
        csv_processor = CSVDataProcessor(source_id=source_id)
        accounts = await csv_processor.process_file(file_path, columns)
        
        # Validate all accounts
        valid_accounts, errors = self.validator.validate_batch(accounts)
        
        if errors:
            logger.warning(f"Found {len(errors)} validation errors in source {source_id}")
            for error in errors[:10]:  # Log first 10 errors
                logger.warning(error)
        
        logger.info(f"Successfully processed {len(valid_accounts)} accounts from CSV source {source_id}")
        return valid_accounts
    
    async def _process_database_source(self, source_id: str, columns: Optional[List[str]] = None,
                                     query: Optional[str] = None, params: Optional[Dict] = None) -> List[AccountData]:
        """Process database source with specified columns."""
        logger.info(f"Processing database source {source_id}")
        logger.info(f"Using columns: {columns}")
        
        if not self.db_connector:
            raise ValueError("Database connector not configured")
        
        if query:
            accounts = await self.db_connector.fetch_accounts(query, params)
        else:
            # Use default query with specified columns
            accounts = await self.db_connector.fetch_accounts_by_source(source_id, columns)
        
        # Validate all accounts
        valid_accounts, errors = self.validator.validate_batch(accounts)
        
        if errors:
            logger.warning(f"Found {len(errors)} validation errors in source {source_id}")
            for error in errors[:10]:  # Log first 10 errors
                logger.warning(error)
        
        logger.info(f"Successfully processed {len(valid_accounts)} accounts from database source {source_id}")
        return valid_accounts
    
    def configure_csv_source(self, source_id: str, file_path: str, 
                           default_columns: Optional[List[str]] = None):
        """Configure a CSV data source with optional default columns."""
        self._source_configs[source_id] = {
            "type": "csv",
            "file_path": file_path,
            "default_columns": default_columns or self._default_columns
        }
        logger.info(f"Configured CSV source {source_id} with columns: {self._source_configs[source_id]['default_columns']}")
    
    def configure_database_source(self, source_id: str, db_connector: DatabaseConnector,
                                default_columns: Optional[List[str]] = None):
        """Configure a database data source with optional default columns."""
        self.db_connector = db_connector
        self._source_configs[source_id] = {
            "type": "database",
            "connector": db_connector,
            "default_columns": default_columns or self._default_columns
        }
        logger.info(f"Configured database source {source_id} with columns: {self._source_configs[source_id]['default_columns']}")
    
    async def process_all_sources(self, columns: Optional[List[str]] = None) -> Dict[str, List[AccountData]]:
        """Process all configured sources with specified columns."""
        results = {}
        
        for source_id, config in self._source_configs.items():
            try:
                # Use source-specific columns or provided columns or default columns
                source_columns = columns or config.get("default_columns", self._default_columns)
                
                if config["type"] == "csv":
                    accounts = await self._process_csv_source(source_id, config["file_path"], source_columns)
                elif config["type"] == "database":
                    accounts = await self._process_database_source(source_id, source_columns)
                else:
                    logger.warning(f"Unknown source type for {source_id}: {config['type']}")
                    continue
                
                results[source_id] = accounts
                
            except Exception as e:
                logger.error(f"Failed to process source {source_id}: {e}")
                results[source_id] = []
        
        return results
    
    async def get_source_summary(self, source_id: str, columns: Optional[List[str]] = None) -> Dict:
        """Get summary statistics for a data source with specified columns."""
        if source_id not in self._source_configs:
            raise ValueError(f"Source {source_id} not configured")
        
        config = self._source_configs[source_id]
        source_columns = columns or config.get("default_columns", self._default_columns)
        
        try:
            if config["type"] == "csv":
                # For CSV, we need to process the file to get summary
                accounts = await self._process_csv_source(source_id, config["file_path"], source_columns)
            elif config["type"] == "database":
                accounts = await self._process_database_source(source_id, source_columns)
            else:
                return {"error": f"Unknown source type: {config['type']}"}
            
            return self._calculate_summary(accounts)
            
        except Exception as e:
            logger.error(f"Error getting summary for source {source_id}: {e}")
            return {"error": str(e)}
    
    def _calculate_summary(self, accounts: List[AccountData]) -> Dict:
        """Calculate summary statistics for accounts."""
        if not accounts:
            return {
                "total_accounts": 0,
                "accounts_with_email": 0,
                "total_deposits": 0.0,
                "total_bets": 0.0,
                "total_withdrawals": 0.0
            }
        
        total_deposits = sum(acc.deposit_amount_sum or 0 for acc in accounts)
        total_bets = sum(acc.bet_money_sum or 0 for acc in accounts)
        total_withdrawals = sum(acc.withdraw_amount_sum or 0 for acc in accounts)
        accounts_with_email = sum(1 for acc in accounts if acc.email)
        
        return {
            "total_accounts": len(accounts),
            "accounts_with_email": accounts_with_email,
            "total_deposits": total_deposits,
            "total_bets": total_bets,
            "total_withdrawals": total_withdrawals,
            "average_deposit": total_deposits / len(accounts) if accounts else 0.0
        } 