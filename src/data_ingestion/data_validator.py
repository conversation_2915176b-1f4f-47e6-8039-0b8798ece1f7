"""
Data validator for the One ID Mapping System.
"""

import re
import logging
from typing import Optional

from ..models.account_data import AccountData


logger = logging.getLogger(__name__)


class DataValidator:
    """Validates account data for quality and consistency."""
    
    def __init__(self):
        # Email validation regex
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        # Account ID validation regex (alphanumeric with optional hyphens/underscores)
        self.account_id_pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
    
    def validate_account(self, account: AccountData) -> bool:
        """Validate individual account data record."""
        try:
            # Check required fields
            if not self._validate_account_id(account.account_id):
                logger.warning(f"Invalid account_id: {account.account_id}")
                return False
            
            # Validate email if present
            if account.email and not self._validate_email(account.email):
                logger.warning(f"Invalid email format: {account.email}")
                return False
            
            # Validate numeric fields
            if not self._validate_numeric_fields(account):
                return False
            
            # Validate time relationships
            if not self._validate_time_relationships(account):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Validation error for account {account.account_id}: {e}")
            return False
    
    def _validate_account_id(self, account_id: str) -> bool:
        """Validate account ID format."""
        if not account_id or len(account_id.strip()) == 0:
            return False
        
        if len(account_id) > 100:  # Reasonable max length
            return False
        
        return bool(self.account_id_pattern.match(account_id))
    
    def _validate_email(self, email: str) -> bool:
        """Validate email format."""
        if not email or len(email.strip()) == 0:
            return False
        
        # Basic email validation
        return bool(self.email_pattern.match(email.lower().strip()))
    
    def _validate_numeric_fields(self, account: AccountData) -> bool:
        """Validate numeric fields are within reasonable ranges."""
        
        # Check deposit amounts
        if account.deposit_amount_sum is not None:
            if account.deposit_amount_sum < 0 or account.deposit_amount_sum > 1e9:  # 1 billion max
                logger.warning(f"Invalid deposit_amount_sum: {account.deposit_amount_sum}")
                return False
        
        # Check bet amounts
        if account.bet_money_sum is not None:
            if account.bet_money_sum < 0 or account.bet_money_sum > 1e9:
                logger.warning(f"Invalid bet_money_sum: {account.bet_money_sum}")
                return False
        
        # Check withdrawal amounts
        if account.withdraw_amount_sum is not None:
            if account.withdraw_amount_sum < 0 or account.withdraw_amount_sum > 1e9:
                logger.warning(f"Invalid withdraw_amount_sum: {account.withdraw_amount_sum}")
                return False
        
        # Check current money
        if account.current_money is not None:
            if account.current_money < 0 or account.current_money > 1e9:
                logger.warning(f"Invalid current_money: {account.current_money}")
                return False
        
        # Check deposit times
        if account.deposit_times_sum is not None:
            if account.deposit_times_sum < 0 or account.deposit_times_sum > 1e6:  # 1 million max
                logger.warning(f"Invalid deposit_times_sum: {account.deposit_times_sum}")
                return False
        
        return True
    
    def _validate_time_relationships(self, account: AccountData) -> bool:
        """Validate time relationships are logical."""
        
        # Registration time should be before or equal to active time
        if account.reg_time and account.active_time:
            if account.reg_time > account.active_time:
                logger.warning(f"Registration time after active time: {account.reg_time} > {account.active_time}")
                return False
        
        # Registration time should be before or equal to update time
        if account.reg_time and account.update_time:
            if account.reg_time > account.update_time:
                logger.warning(f"Registration time after update time: {account.reg_time} > {account.update_time}")
                return False
        
        # Active time should be before or equal to update time
        if account.active_time and account.update_time:
            if account.active_time > account.update_time:
                logger.warning(f"Active time after update time: {account.active_time} > {account.update_time}")
                return False
        
        return True
    
    def validate_batch(self, accounts: list[AccountData]) -> tuple[list[AccountData], list[str]]:
        """Validate a batch of accounts and return valid ones with error messages."""
        valid_accounts = []
        errors = []
        
        for account in accounts:
            if self.validate_account(account):
                valid_accounts.append(account)
            else:
                errors.append(f"Validation failed for account {account.account_id}")
        
        return valid_accounts, errors 