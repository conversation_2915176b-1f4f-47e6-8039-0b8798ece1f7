"""
CSV data processor for the One ID Mapping System.
"""

import csv
import logging
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import os

from ..models.account_data import AccountData
from .data_validator import DataValidator


logger = logging.getLogger(__name__)


class CSVDataProcessor:
    """Processes CSV files and converts them to AccountData objects."""
    
    def __init__(self, source_id: str = ""):
        self.source_id = source_id
        self.validator = DataValidator()
        
    def process_raw_data_to_csv(self, columns: Optional[List[str]] = None, chunk_size: int = 100_000) -> Dict[str, str]:
        """
        Process all CSV files from raw_data directory and output two CSV files:
        1. account_email_mapping.csv - contains account_id and email
        2. account_properties.csv - contains account_id and other properties
        
        Args:
            columns: List of columns to extract from raw data
            chunk_size: Number of rows to process at once for large files
            
        Returns:
            Dictionary with output file paths
        """
        raw_data_dir = Path("raw_data")
        processed_data_dir = Path("processed_data")
        
        # Ensure directories exist
        raw_data_dir.mkdir(exist_ok=True)
        processed_data_dir.mkdir(exist_ok=True)
        
        # Prepare output file paths
        mapping_path = processed_data_dir / "account_email_mapping.csv"
        properties_path = processed_data_dir / "account_properties.csv"
        
        # Get all csv files in raw_data
        csv_files = list(raw_data_dir.glob("*_results.csv"))
        if not csv_files:
            logger.warning("No *_results.csv files found in raw_data directory")
            return {
                "account_email_mapping": str(mapping_path),
                "account_properties": str(properties_path)
            }
        
        logger.info(f"Found {len(csv_files)} CSV files to process")
        
        # Collect all data first, then write once
        all_mapping_data = []
        all_properties_data = []
        
        total_accounts = 0
        
        for csv_file in csv_files:
            # Extract project_id from filename
            project_id = csv_file.stem.split('_')[0]
            logger.info(f"Processing project {project_id} from {csv_file.name}")
            
            try:
                # Read in chunks for large files
                for chunk in pd.read_csv(csv_file, chunksize=chunk_size):
                    # Filter columns if specified
                    if columns:
                        available_columns = [col for col in columns if col in chunk.columns]
                        if not available_columns:
                            logger.warning(f"  No requested columns found in {csv_file.name}, skipping")
                            continue
                        chunk = chunk[available_columns]
                    
                    # Generate new account_id with project prefix
                    if 'account_id' in chunk.columns:
                        chunk['account_id'] = chunk['account_id'].astype(str).apply(lambda x: f"{project_id}_{x}")
                    else:
                        logger.warning(f"  No account_id column found in {csv_file.name}")
                        continue
                    
                    # Prepare mapping and properties DataFrames
                    if 'email' in chunk.columns:
                        mapping_df = chunk[['account_id', 'email']]
                        # Remove rows where email is null or empty
                        mapping_df = mapping_df.dropna(subset=['email'])
                        mapping_df = mapping_df[mapping_df['email'].str.strip() != '']
                    else:
                        # If no email column, create empty dataframe with account_id
                        mapping_df = chunk[['account_id']].copy()
                        mapping_df['email'] = None
                    
                    # Create properties dataframe (all columns except email)
                    properties_cols = [col for col in chunk.columns if col != 'email']
                    properties_df = chunk[properties_cols]
                    
                    # Collect data instead of writing immediately
                    all_mapping_data.append(mapping_df)
                    all_properties_data.append(properties_df)
                    
                    total_accounts += len(properties_df)
                
                logger.info(f"✓ Completed processing {csv_file.name}")
                
            except Exception as e:
                logger.error(f"Failed to process {csv_file}: {e}")
        
        # Write all data at once (overwrite mode with headers)
        if all_mapping_data:
            final_mapping_df = pd.concat(all_mapping_data, ignore_index=True)
            final_properties_df = pd.concat(all_properties_data, ignore_index=True)
            
            final_mapping_df.to_csv(mapping_path, index=False)
            final_properties_df.to_csv(properties_path, index=False)
        
        logger.info(f"✓ Processing complete. Total accounts: {total_accounts}")
        logger.info(f"✓ Output files:\n  - {mapping_path}\n  - {properties_path}")
        
        return {
            "account_email_mapping": str(mapping_path),
            "account_properties": str(properties_path)
        }
    
    async def process_file(self, file_path: str, columns: Optional[List[str]] = None) -> List[AccountData]:
        """Process CSV file and return validated account data with specified columns."""
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"CSV file not found: {file_path}")
        
        accounts = []
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                # Filter columns if specified
                if columns:
                    # Check if all requested columns exist in the CSV
                    available_columns = reader.fieldnames
                    missing_columns = [col for col in columns if col not in available_columns]
                    if missing_columns:
                        logger.warning(f"Missing columns in CSV: {missing_columns}")
                        # Only keep columns that exist in the CSV
                        columns = [col for col in columns if col in available_columns]
                    
                    logger.info(f"Processing columns: {columns}")
                
                for row_num, row in enumerate(reader, start=2):
                    try:
                        # Filter row to only include specified columns if provided
                        if columns:
                            filtered_row = {col: row.get(col, '') for col in columns}
                        else:
                            filtered_row = row
                        
                        account = self._transform_row_to_account(filtered_row)
                        if self.validator.validate_account(account):
                            accounts.append(account)
                        else:
                            errors.append(f"Row {row_num}: Validation failed")
                    except Exception as e:
                        error_msg = f"Row {row_num}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)
            
            logger.info(f"Processed {len(accounts)} accounts from {file_path}")
            if errors:
                logger.warning(f"Found {len(errors)} errors during processing")
                
        except Exception as e:
            logger.error(f"Error processing CSV file {file_path}: {e}")
            raise
        
        return accounts
    
    def _transform_row_to_account(self, row: Dict[str, str]) -> AccountData:
        """Transform CSV row to AccountData object."""
        return AccountData(
            account_id=row['account_id'],
            email=row.get('email'),
            deposit_amount_sum=self._parse_float(row.get('deposit_amount_sum')),
            deposit_times_sum=self._parse_int(row.get('deposit_times_sum')),
            bet_money_sum=self._parse_float(row.get('bet_money_sum')),
            withdraw_amount_sum=self._parse_float(row.get('withdraw_amount_sum')),
            active_time=self._parse_datetime(row.get('active_time')),
            reg_time=self._parse_datetime(row.get('reg_time')),
            current_money=self._parse_float(row.get('current_money')),
            update_time=self._parse_datetime(row.get('update_time')),
            source_id=self.source_id
        )
    
    def _parse_float(self, value: Optional[str]) -> Optional[float]:
        """Parse string to float, return None if empty or invalid."""
        if not value or value.strip() == '':
            return None
        try:
            return float(value.strip())
        except ValueError:
            return None
    
    def _parse_int(self, value: Optional[str]) -> Optional[int]:
        """Parse string to int, return None if empty or invalid."""
        if not value or value.strip() == '':
            return None
        try:
            return int(float(value.strip()))  # Handle cases like "1.0"
        except ValueError:
            return None
    
    def _parse_datetime(self, value: Optional[str]) -> Optional[datetime]:
        """Parse string to datetime, return None if empty or invalid."""
        if not value or value.strip() == '':
            return None
        
        # Common datetime formats
        formats = [
            '%Y-%m-%d %H:%M:%S.%f',  # 2024-12-20 03:35:28.096
            '%Y-%m-%d %H:%M:%S',     # 2024-12-20 03:35:28
            '%Y-%m-%dT%H:%M:%S.%f',  # 2024-12-20T03:35:28.096
            '%Y-%m-%dT%H:%M:%S',     # 2024-12-20T03:35:28
            '%Y-%m-%d',              # 2024-12-20
            '%d/%m/%Y %H:%M:%S',     # 20/12/2024 03:35:28
            '%d/%m/%Y',              # 20/12/2024
            '%m/%d/%Y %H:%M:%S',     # 12/20/2024 03:35:28
            '%m/%d/%Y',              # 12/20/2024
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(value.strip(), fmt)
            except ValueError:
                continue
        
        logger.warning(f"Could not parse datetime: {value}")
        return None 