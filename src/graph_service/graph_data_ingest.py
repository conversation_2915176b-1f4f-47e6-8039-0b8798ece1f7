"""
Graph database manager for Neo4j operations.
"""

import logging
from typing import Optional

from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, AuthError

from ..models.account_data import AccountData
from ..models.user_data import UserData
from ..config.database_config import Neo4jConfig
from ..config.processing_config import ProcessingConfig
# Removed unused imports - these functions don't exist in data_processor


logger = logging.getLogger(__name__)


class GraphDatabaseError(Exception):
    """Raised when graph database operations fail."""
    pass


class GraphDataIngest:
    """Manages Neo4j graph database data ingestion operations."""
    
    def __init__(self, config: Neo4jConfig, processing_config: ProcessingConfig = None):
        self.config = config
        self.processing_config = processing_config or ProcessingConfig()
        self.driver = None
        self.session_pool = None
    
    async def connect(self) -> bool:
        """Establish connection to Neo4j database."""
        try:
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                max_connection_pool_size=self.config.max_connection_pool_size,
                connection_timeout=self.config.connection_timeout,
                max_transaction_retry_time=self.config.max_transaction_retry_time
            )
            
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            
            logger.info(f"Connected to Neo4j database: {self.config.uri}")
            return True
            
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise GraphDatabaseError(f"Neo4j connection failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error connecting to Neo4j: {e}")
            raise GraphDatabaseError(f"Connection error: {e}")
    
    async def disconnect(self):
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j connection closed")
    
    async def ingest_data_from_csv(self, account_email_mapping_file: str = None, account_properties_file: str = None) -> dict:
        """
        Ingest data from CSV files using optimized Cypher scripts.
        
        Args:
            account_email_mapping_file: Path to the account-email mapping CSV file (optional, uses config if not provided)
            account_properties_file: Path to the account properties CSV file (optional, uses config if not provided)
            
        Returns:
            Dictionary with ingestion results
        """
        # Use configuration values if not provided
        account_email_mapping_file = account_email_mapping_file or self.processing_config.file_config.account_email_mapping_file
        account_properties_file = account_properties_file or self.processing_config.file_config.account_properties_file
        
        try:
            with self.driver.session() as session:
                logger.info("Starting data ingestion from CSV files...")
                logger.info(f"Using account-email mapping file: {account_email_mapping_file}")
                logger.info(f"Using account properties file: {account_properties_file}")
                
                # Step 1: Create accounts from account-email mapping
                logger.info("Step 1: Creating accounts from account-email mapping...")
                step1_query = f"""
                :auto LOAD CSV WITH HEADERS FROM 'file:///{account_email_mapping_file}' AS row
                CALL {{
                  WITH row
                  MERGE (a:account {{account_id: row.account_id}})
                }} IN TRANSACTIONS OF 100000 ROWS;
                """
                session.run(step1_query)
                logger.info("✓ Step 1 completed: Accounts created")
                
                # Step 2: Create emails from account-email mapping
                logger.info("Step 2: Creating emails from account-email mapping...")
                step2_query = f"""
                :auto LOAD CSV WITH HEADERS FROM 'file:///{account_email_mapping_file}' AS row
                CALL {{
                  WITH row
                  MERGE (e:email {{email: row.email}})
                }} IN TRANSACTIONS OF 100000 ROWS;
                """
                session.run(step2_query)
                logger.info("✓ Step 2 completed: Emails created")
                
                # Step 3: Create relationships and set account properties
                logger.info("Step 3: Creating relationships and setting account properties...")
                step3_query = f"""
                :auto LOAD CSV WITH HEADERS FROM 'file:///{account_properties_file}' AS row
                CALL {{
                  WITH row
                  MERGE (a:account {{account_id: row.account_id}})
                  SET a.reg_time = row.reg_time,
                      a.deposit_amount_sum = row.deposit_amount_sum
                }} IN TRANSACTIONS OF 100000 ROWS;
                """
                session.run(step3_query)
                logger.info("✓ Step 3 completed: Account properties set")
                
                # Step 4: Create HAS_EMAIL relationships
                logger.info("Step 4: Creating HAS_EMAIL relationships...")
                step4_query = f"""
                :auto LOAD CSV WITH HEADERS FROM 'file:///{account_email_mapping_file}' AS row
                CALL {{
                  WITH row 
                  MATCH (a:account {{account_id: row.account_id}})
                  MATCH (e:email {{email: row.email}})
                  MERGE (a)-[:HAS_EMAIL]->(e)
                }} IN TRANSACTIONS OF 100000 ROWS;
                """
                session.run(step4_query)
                logger.info("✓ Step 4 completed: HAS_EMAIL relationships created")
                
                # Get statistics
                result = session.run("MATCH (a:account) RETURN count(a) as account_count")
                account_count = result.single()["account_count"]
                
                result = session.run("MATCH (e:email) RETURN count(e) as email_count")
                email_count = result.single()["email_count"]
                
                result = session.run("MATCH ()-[r:HAS_EMAIL]->() RETURN count(r) as relationship_count")
                relationship_count = result.single()["relationship_count"]
                
                logger.info("✓ Data ingestion completed successfully")
                
                return {
                    "success": True,
                    "accounts_created": account_count,
                    "emails_created": email_count,
                    "relationships_created": relationship_count,
                    "message": f"Successfully ingested {account_count} accounts, {email_count} emails, and {relationship_count} relationships"
                }
                
        except Exception as e:
            logger.error(f"Failed to ingest data from CSV: {e}")
            raise GraphDatabaseError(f"Data ingestion failed: {e}")
    
    async def get_ingestion_statistics(self) -> dict:
        """
        Get current database statistics.
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with self.driver.session() as session:
                # Get account count
                result = session.run("MATCH (a:account) RETURN count(a) as account_count")
                account_count = result.single()["account_count"]
                
                # Get email count
                result = session.run("MATCH (e:email) RETURN count(e) as email_count")
                email_count = result.single()["email_count"]
                
                # Get relationship count
                result = session.run("MATCH ()-[r:HAS_EMAIL]->() RETURN count(r) as relationship_count")
                relationship_count = result.single()["relationship_count"]
                
                # Get sample data
                result = session.run("MATCH (a:account) RETURN a.account_id LIMIT 5")
                sample_accounts = [record["a.account_id"] for record in result]
                
                return {
                    "account_count": account_count,
                    "email_count": email_count,
                    "relationship_count": relationship_count,
                    "sample_accounts": sample_accounts
                }
                
        except Exception as e:
            logger.error(f"Failed to get ingestion statistics: {e}")
            raise GraphDatabaseError(f"Statistics retrieval failed: {e}")
    
    async def clean_all_data(self) -> dict:
        """
        Clean all data from the database using optimized Cypher script.
        
        Returns:
            Dictionary with cleaning results
        """
        try:
            with self.driver.session() as session:
                logger.info("Starting data cleaning process...")
                
                # Get initial statistics before cleaning
                result = session.run("MATCH (n) RETURN count(n) as total_nodes")
                initial_node_count = result.single()["total_nodes"]
                
                result = session.run("MATCH ()-[r]->() RETURN count(r) as total_relationships")
                initial_relationship_count = result.single()["total_relationships"]
                
                logger.info(f"Found {initial_node_count} nodes and {initial_relationship_count} relationships to clean")
                
                # Execute the cleaning script
                clean_query = """
                MATCH (n)
                CALL {
                  WITH n
                  DETACH DELETE n
                } IN TRANSACTIONS OF 10000 ROWS;
                """
                
                session.run(clean_query)
                logger.info("✓ Data cleaning completed")
                
                # Verify cleaning results
                result = session.run("MATCH (n) RETURN count(n) as remaining_nodes")
                remaining_nodes = result.single()["remaining_nodes"]
                
                result = session.run("MATCH ()-[r]->() RETURN count(r) as remaining_relationships")
                remaining_relationships = result.single()["remaining_relationships"]
                
                nodes_deleted = initial_node_count - remaining_nodes
                relationships_deleted = initial_relationship_count - remaining_relationships
                
                logger.info(f"✓ Successfully deleted {nodes_deleted} nodes and {relationships_deleted} relationships")
                
                return {
                    "success": True,
                    "nodes_deleted": nodes_deleted,
                    "relationships_deleted": relationships_deleted,
                    "remaining_nodes": remaining_nodes,
                    "remaining_relationships": remaining_relationships,
                    "message": f"Successfully cleaned {nodes_deleted} nodes and {relationships_deleted} relationships"
                }
                
        except Exception as e:
            logger.error(f"Failed to clean data: {e}")
            raise GraphDatabaseError(f"Data cleaning failed: {e}")
    
    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False 