"""
Data processing pipeline for the One ID Mapping System.
"""

import logging
from typing import Dict, List

from ..models.account_data import AccountData
from ..models.merge_data import MergeResult
from ..models.api_models import PipelineResult
from ..data_ingestion.ingestion_service import DataIngestionService
from ..user_merging.merging_service import UserMergingService
from ..graph_service.graph_data_ingest import GraphDataIngest


logger = logging.getLogger(__name__)


class PipelineError(Exception):
    """Raised when pipeline execution fails."""
    pass


class DataProcessingPipeline:
    """Orchestrates the entire data processing workflow."""
    
    def __init__(self, 
                 ingestion_service: DataIngestionService,
                 merging_service: UserMergingService,
                 graph_manager: GraphDataIngest):
        self.ingestion_service = ingestion_service
        self.merging_service = merging_service
        self.graph_manager = graph_manager
    
    async def process_data_source(self, source_id: str) -> PipelineResult:
        """Execute complete data processing pipeline for a source."""
        logger.info(f"Starting pipeline for source: {source_id}")
        
        try:
            # Step 1: Ingest data
            logger.info(f"Step 1: Ingesting data from source {source_id}")
            accounts = await self.ingestion_service.process_source(source_id)
            
            if not accounts:
                logger.warning(f"No accounts found in source {source_id}")
                return PipelineResult(
                    success=True,
                    accounts_processed=0,
                    users_created=0,
                    message="No accounts found in source"
                )
            
            # Step 2: Find merge candidates
            logger.info(f"Step 2: Finding merge candidates from {len(accounts)} accounts")
            merge_groups = await self.merging_service.find_merge_candidates(accounts)
            
            if not merge_groups:
                logger.info(f"No merge candidates found for source {source_id}")
                return PipelineResult(
                    success=True,
                    accounts_processed=len(accounts),
                    users_created=0,
                    message="No merge candidates found"
                )
            
            # Step 3: Execute merges
            logger.info(f"Step 3: Executing {len(merge_groups)} merges")
            merge_results = await self.merging_service.execute_merging(source_id, accounts)
            
            # Step 4: Calculate statistics
            successful_merges = [r for r in merge_results if r.success]
            failed_merges = [r for r in merge_results if not r.success]
            
            total_accounts_merged = sum(r.accounts_merged for r in successful_merges)
            users_created = len(successful_merges)
            
            logger.info(f"Pipeline completed for source {source_id}:")
            logger.info(f"  - Accounts processed: {len(accounts)}")
            logger.info(f"  - Users created: {users_created}")
            logger.info(f"  - Accounts merged: {total_accounts_merged}")
            logger.info(f"  - Failed merges: {len(failed_merges)}")
            
            return PipelineResult(
                success=True,
                accounts_processed=len(accounts),
                users_created=users_created,
                message=f"Pipeline completed successfully. {users_created} users created from {total_accounts_merged} accounts.",
                errors=[r.error_message for r in failed_merges] if failed_merges else []
            )
            
        except Exception as e:
            logger.error(f"Pipeline failed for source {source_id}: {e}")
            return PipelineResult(
                success=False,
                accounts_processed=0,
                users_created=0,
                message=f"Pipeline failed: {str(e)}",
                errors=[str(e)]
            )
    
    async def process_all_sources(self) -> Dict[str, PipelineResult]:
        """Process all configured data sources."""
        logger.info("Processing all configured data sources")
        
        results = {}
        
        # Get all configured sources from ingestion service
        for source_id, config in self.ingestion_service._source_configs.items():
            try:
                result = await self.process_data_source(source_id)
                results[source_id] = result
            except Exception as e:
                logger.error(f"Failed to process source {source_id}: {e}")
                results[source_id] = PipelineResult(
                    success=False,
                    accounts_processed=0,
                    users_created=0,
                    message=f"Source processing failed: {str(e)}",
                    errors=[str(e)]
                )
        
        return results
    
    async def preview_pipeline(self, source_id: str) -> Dict:
        """Preview pipeline execution without committing changes."""
        logger.info(f"Previewing pipeline for source: {source_id}")
        
        try:
            # Step 1: Ingest data
            accounts = await self.ingestion_service.process_source(source_id)
            
            if not accounts:
                return {
                    "source_id": source_id,
                    "accounts_processed": 0,
                    "merge_groups": [],
                    "statistics": {},
                    "message": "No accounts found in source"
                }
            
            # Step 2: Find merge candidates (preview only)
            merge_groups = await self.merging_service.find_merge_candidates(accounts)
            
            # Step 3: Get merging preview
            preview_data = await self.merging_service.preview_merging(accounts)
            
            return {
                "source_id": source_id,
                "accounts_processed": len(accounts),
                "merge_groups": len(merge_groups),
                "preview_data": preview_data,
                "message": f"Preview completed. {len(merge_groups)} merge groups found."
            }
            
        except Exception as e:
            logger.error(f"Pipeline preview failed for source {source_id}: {e}")
            return {
                "source_id": source_id,
                "error": str(e),
                "message": f"Preview failed: {str(e)}"
            }
    
    async def get_pipeline_status(self) -> Dict:
        """Get overall pipeline status and statistics."""
        try:
            # Test database connection
            db_connected = await self.graph_manager.test_connection()
            
            # Get source configurations
            source_count = len(self.ingestion_service._source_configs)
            
            return {
                "status": "ready" if db_connected else "database_disconnected",
                "database_connected": db_connected,
                "configured_sources": source_count,
                "sources": list(self.ingestion_service._source_configs.keys())
            }
            
        except Exception as e:
            logger.error(f"Failed to get pipeline status: {e}")
            return {
                "status": "error",
                "error": str(e)
            } 