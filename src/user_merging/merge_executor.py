"""
Merge executor for the One ID Mapping System.
"""

import logging
import uuid
from datetime import datetime
from typing import Optional, Dict

from ..models.merge_data import MergeGroup, MergeResult
from ..models.user_data import UserData


logger = logging.getLogger(__name__)


class MergeExecutionError(Exception):
    """Raised when merge execution fails."""
    pass


class MergeExecutor:
    """Executes merge operations in the graph database."""
    
    def __init__(self, graph_manager):
        self.graph_manager = graph_manager
        self.audit_logger = None  # Will be implemented later
    
    async def execute_merge(self, merge_group: MergeGroup) -> MergeResult:
        """Execute the merge operation."""
        logger.info(f"Executing merge {merge_group.merge_id} with {len(merge_group.accounts)} accounts")
        
        try:
            # Generate unique user ID
            user_id = self._generate_user_id()
            
            # Create user data
            user_data = self._create_user_data(user_id, merge_group)
            
            # Create user node in graph
            await self.graph_manager.create_user_node(user_data)
            
            # Create account nodes and link them to user
            for account in merge_group.accounts:
                await self.graph_manager.create_account_node(account)
                await self.graph_manager.link_account_to_user(user_id, account.account_id)
                
                # Create email node and link if email exists
                if account.email:
                    await self.graph_manager.create_email_node(account.email)
                    await self.graph_manager.link_account_to_email(account.account_id, account.email)
            
            # Log merge operation (audit logging will be implemented later)
            await self._log_merge_operation(merge_group, user_id)
            
            logger.info(f"Successfully executed merge {merge_group.merge_id}, created user {user_id}")
            
            return MergeResult(
                success=True,
                user_id=user_id,
                accounts_merged=len(merge_group.accounts),
                merge_id=merge_group.merge_id
            )
            
        except Exception as e:
            logger.error(f"Failed to execute merge {merge_group.merge_id}: {e}")
            await self._rollback_merge(merge_group.merge_id)
            raise MergeExecutionError(f"Merge failed: {e}")
    
    def _generate_user_id(self) -> str:
        """Generate a unique user ID."""
        return f"user_{uuid.uuid4().hex[:16]}"
    
    def _create_user_data(self, user_id: str, merge_group: MergeGroup) -> UserData:
        """Create user data from merge group."""
        accounts = merge_group.accounts
        
        # Calculate aggregated financial metrics
        total_deposits = sum(acc.deposit_amount_sum or 0 for acc in accounts)
        total_bets = sum(acc.bet_money_sum or 0 for acc in accounts)
        
        # Find earliest registration time
        reg_times = [acc.reg_time for acc in accounts if acc.reg_time]
        earliest_reg_time = min(reg_times) if reg_times else None
        
        # Find latest activity time
        active_times = [acc.active_time for acc in accounts if acc.active_time]
        latest_activity = max(active_times) if active_times else None
        
        # Count unique emails
        unique_emails = set(acc.email.lower().strip() for acc in accounts if acc.email)
        email_count = len(unique_emails)
        
        return UserData(
            user_id=user_id,
            total_deposits=total_deposits,
            total_bets=total_bets,
            earliest_reg_time=earliest_reg_time,
            latest_activity=latest_activity,
            account_count=len(accounts),
            email_count=email_count
        )
    
    async def _log_merge_operation(self, merge_group: MergeGroup, user_id: str):
        """Log merge operation for audit purposes."""
        # This will be implemented when audit logging is added
        logger.info(f"Merge logged: {merge_group.merge_id} -> {user_id}")
    
    async def _rollback_merge(self, merge_id: str):
        """Rollback a failed merge operation."""
        try:
            # This will be implemented when rollback functionality is added
            logger.warning(f"Rollback requested for merge {merge_id}")
        except Exception as e:
            logger.error(f"Failed to rollback merge {merge_id}: {e}")
    
    async def execute_merge_batch(self, merge_groups: list[MergeGroup]) -> list[MergeResult]:
        """Execute multiple merge operations in batch."""
        results = []
        
        for merge_group in merge_groups:
            try:
                result = await self.execute_merge(merge_group)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to execute merge {merge_group.merge_id}: {e}")
                results.append(MergeResult(
                    success=False,
                    merge_id=merge_group.merge_id,
                    error_message=str(e)
                ))
        
        return results
    
    async def validate_merge_group(self, merge_group: MergeGroup) -> bool:
        """Validate that a merge group can be executed."""
        try:
            # Check if all accounts exist and are not already merged
            for account in merge_group.accounts:
                # Check if account already belongs to a user
                existing_user = await self.graph_manager.find_user_by_account(account.account_id)
                if existing_user:
                    logger.warning(f"Account {account.account_id} already belongs to user {existing_user}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating merge group {merge_group.merge_id}: {e}")
            return False
    
    async def get_merge_status(self, merge_id: str) -> Optional[Dict]:
        """Get the status of a merge operation."""
        # This will be implemented when merge tracking is added
        logger.info(f"Getting status for merge {merge_id}")
        return {
            "merge_id": merge_id,
            "status": "completed",  # Placeholder
            "timestamp": datetime.utcnow()
        } 