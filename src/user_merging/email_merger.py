"""
Email-based user merging for the One ID Mapping System.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List

from ..models.account_data import AccountData
from ..models.merge_data import MergeGroup


logger = logging.getLogger(__name__)


class EmailBasedMerger:
    """Identifies and groups accounts that should be merged based on email addresses."""
    
    def __init__(self, confidence_threshold: float = 0.8):
        self.confidence_threshold = confidence_threshold
    
    async def find_merge_candidates(self, accounts: List[AccountData]) -> List[MergeGroup]:
        """Find accounts that should be merged based on email."""
        logger.info(f"Finding merge candidates from {len(accounts)} accounts")
        
        # Group accounts by email
        email_groups = self._group_accounts_by_email(accounts)
        
        # Create merge groups for accounts with same email
        merge_groups = []
        
        for email, account_list in email_groups.items():
            if len(account_list) > 1:
                confidence = self._calculate_confidence(account_list)
                
                if confidence >= self.confidence_threshold:
                    merge_group = MergeGroup(
                        accounts=account_list,
                        confidence_score=confidence,
                        merge_criteria="email",
                        merge_id=str(uuid.uuid4())
                    )
                    merge_groups.append(merge_group)
                    logger.info(f"Found merge group for email {email}: {len(account_list)} accounts, confidence: {confidence:.3f}")
                else:
                    logger.debug(f"Low confidence merge group for email {email}: {confidence:.3f} < {self.confidence_threshold}")
        
        logger.info(f"Found {len(merge_groups)} merge groups with confidence >= {self.confidence_threshold}")
        return merge_groups
    
    def _group_accounts_by_email(self, accounts: List[AccountData]) -> Dict[str, List[AccountData]]:
        """Group accounts by email address."""
        groups = {}
        
        for account in accounts:
            if account.email:
                # Normalize email: lowercase and strip whitespace
                email = account.email.lower().strip()
                
                if email not in groups:
                    groups[email] = []
                groups[email].append(account)
        
        return groups
    
    def _calculate_confidence(self, accounts: List[AccountData]) -> float:
        """Calculate confidence score for merging accounts."""
        if len(accounts) < 2:
            return 0.0
        
        # Base confidence from email match
        base_confidence = 1.0
        
        # Time proximity factor
        time_confidence = self._calculate_time_proximity(accounts)
        
        # Financial behavior similarity
        financial_confidence = self._calculate_financial_similarity(accounts)
        
        # Registration pattern similarity
        registration_confidence = self._calculate_registration_similarity(accounts)
        
        # Weighted average of all factors
        total_confidence = (
            base_confidence * 0.4 +
            time_confidence * 0.2 +
            financial_confidence * 0.2 +
            registration_confidence * 0.2
        )
        
        return min(total_confidence, 1.0)
    
    def _calculate_time_proximity(self, accounts: List[AccountData]) -> float:
        """Calculate confidence based on time proximity of activities."""
        if not accounts:
            return 0.0
        
        # Get all registration times
        reg_times = [acc.reg_time for acc in accounts if acc.reg_time]
        if len(reg_times) < 2:
            return 0.5  # Neutral if we don't have enough time data
        
        # Calculate time differences
        reg_times.sort()
        time_diffs = []
        for i in range(1, len(reg_times)):
            diff = abs((reg_times[i] - reg_times[i-1]).total_seconds())
            time_diffs.append(diff)
        
        if not time_diffs:
            return 0.5
        
        # Convert to days and calculate confidence
        avg_diff_days = sum(time_diffs) / len(time_diffs) / (24 * 3600)
        
        # Higher confidence for accounts created within 30 days
        if avg_diff_days <= 30:
            return 1.0
        elif avg_diff_days <= 90:
            return 0.8
        elif avg_diff_days <= 180:
            return 0.6
        else:
            return 0.3
    
    def _calculate_financial_similarity(self, accounts: List[AccountData]) -> float:
        """Calculate confidence based on financial behavior similarity."""
        if not accounts:
            return 0.0
        
        # Calculate average deposit amounts
        deposit_amounts = [acc.deposit_amount_sum or 0 for acc in accounts]
        avg_deposit = sum(deposit_amounts) / len(deposit_amounts)
        
        if avg_deposit == 0:
            return 0.5  # Neutral if no deposits
        
        # Calculate coefficient of variation (lower = more similar)
        variance = sum((amount - avg_deposit) ** 2 for amount in deposit_amounts) / len(deposit_amounts)
        std_dev = variance ** 0.5
        cv = std_dev / avg_deposit if avg_deposit > 0 else 0
        
        # Higher confidence for similar deposit patterns
        if cv <= 0.5:
            return 1.0
        elif cv <= 1.0:
            return 0.8
        elif cv <= 2.0:
            return 0.6
        else:
            return 0.3
    
    def _calculate_registration_similarity(self, accounts: List[AccountData]) -> float:
        """Calculate confidence based on registration pattern similarity."""
        if not accounts:
            return 0.0
        
        # Check if accounts have similar registration patterns
        # (e.g., similar time of day, day of week)
        reg_times = [acc.reg_time for acc in accounts if acc.reg_time]
        
        if len(reg_times) < 2:
            return 0.5
        
        # Check if registrations happened at similar times of day
        hours = [dt.hour for dt in reg_times]
        hour_variance = sum((hour - sum(hours) / len(hours)) ** 2 for hour in hours) / len(hours)
        
        # Higher confidence for similar registration times
        if hour_variance <= 4:  # Within 2 hours
            return 1.0
        elif hour_variance <= 16:  # Within 4 hours
            return 0.8
        elif hour_variance <= 36:  # Within 6 hours
            return 0.6
        else:
            return 0.3
    
    def get_merge_statistics(self, merge_groups: List[MergeGroup]) -> Dict:
        """Get statistics about merge groups."""
        if not merge_groups:
            return {
                "total_groups": 0,
                "total_accounts": 0,
                "average_group_size": 0,
                "average_confidence": 0,
                "confidence_distribution": {}
            }
        
        total_accounts = sum(len(group.accounts) for group in merge_groups)
        avg_group_size = total_accounts / len(merge_groups)
        avg_confidence = sum(group.confidence_score for group in merge_groups) / len(merge_groups)
        
        # Confidence distribution
        confidence_ranges = {
            "0.8-0.9": 0,
            "0.9-1.0": 0
        }
        
        for group in merge_groups:
            if 0.8 <= group.confidence_score < 0.9:
                confidence_ranges["0.8-0.9"] += 1
            elif group.confidence_score >= 0.9:
                confidence_ranges["0.9-1.0"] += 1
        
        return {
            "total_groups": len(merge_groups),
            "total_accounts": total_accounts,
            "average_group_size": avg_group_size,
            "average_confidence": avg_confidence,
            "confidence_distribution": confidence_ranges
        } 