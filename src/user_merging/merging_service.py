"""
Main user merging service for the One ID Mapping System.
"""

import logging
from typing import List

from ..models.account_data import AccountData
from ..models.merge_data import MergeGroup, MergeResult
from .email_merger import EmailBasedMerger
from .merge_executor import MergeExecutor


logger = logging.getLogger(__name__)


class UserMergingService:
    """Main service for user merging operations."""
    
    def __init__(self, graph_manager, confidence_threshold: float = 0.8):
        self.email_merger = EmailBasedMerger(confidence_threshold=confidence_threshold)
        self.merge_executor = MergeExecutor(graph_manager)
    
    async def find_merge_candidates(self, accounts: List[AccountData]) -> List[MergeGroup]:
        """Find accounts that should be merged."""
        logger.info(f"Finding merge candidates from {len(accounts)} accounts")
        
        # Use email-based merging
        merge_groups = await self.email_merger.find_merge_candidates(accounts)
        
        # Validate merge groups
        valid_groups = []
        for group in merge_groups:
            if await self.merge_executor.validate_merge_group(group):
                valid_groups.append(group)
            else:
                logger.warning(f"Invalid merge group {group.merge_id}, skipping")
        
        logger.info(f"Found {len(valid_groups)} valid merge groups")
        return valid_groups
    
    async def execute_merging(self, source_id: str, accounts: List[AccountData]) -> List[MergeResult]:
        """Execute complete merging process for a data source."""
        logger.info(f"Executing merging for source {source_id} with {len(accounts)} accounts")
        
        try:
            # Step 1: Find merge candidates
            merge_groups = await self.find_merge_candidates(accounts)
            
            if not merge_groups:
                logger.info(f"No merge candidates found for source {source_id}")
                return []
            
            # Step 2: Execute merges
            merge_results = await self.merge_executor.execute_merge_batch(merge_groups)
            
            # Step 3: Calculate statistics
            successful_merges = [r for r in merge_results if r.success]
            failed_merges = [r for r in merge_results if not r.success]
            
            logger.info(f"Merging completed for source {source_id}:")
            logger.info(f"  - Successful merges: {len(successful_merges)}")
            logger.info(f"  - Failed merges: {len(failed_merges)}")
            logger.info(f"  - Total accounts merged: {sum(r.accounts_merged for r in successful_merges)}")
            
            return merge_results
            
        except Exception as e:
            logger.error(f"Error executing merging for source {source_id}: {e}")
            raise
    
    async def get_merging_statistics(self, merge_groups: List[MergeGroup]) -> dict:
        """Get statistics about merging operations."""
        return self.email_merger.get_merge_statistics(merge_groups)
    
    async def preview_merging(self, accounts: List[AccountData]) -> dict:
        """Preview merging without executing it."""
        logger.info(f"Previewing merging for {len(accounts)} accounts")
        
        # Find merge candidates
        merge_groups = await self.find_merge_candidates(accounts)
        
        # Get statistics
        stats = await self.get_merging_statistics(merge_groups)
        
        # Prepare preview data
        preview_data = []
        for group in merge_groups:
            preview_data.append({
                "merge_id": group.merge_id,
                "confidence_score": group.confidence_score,
                "account_count": len(group.accounts),
                "account_ids": [acc.account_id for acc in group.accounts],
                "emails": group.get_emails(),
                "total_deposits": sum(acc.deposit_amount_sum or 0 for acc in group.accounts),
                "total_bets": sum(acc.bet_money_sum or 0 for acc in group.accounts)
            })
        
        return {
            "preview_data": preview_data,
            "statistics": stats,
            "total_accounts": len(accounts),
            "accounts_in_merge_groups": sum(len(group.accounts) for group in merge_groups),
            "standalone_accounts": len(accounts) - sum(len(group.accounts) for group in merge_groups)
        }
    
    async def execute_single_merge(self, merge_group: MergeGroup) -> MergeResult:
        """Execute a single merge operation."""
        logger.info(f"Executing single merge {merge_group.merge_id}")
        
        # Validate the merge group
        if not await self.merge_executor.validate_merge_group(merge_group):
            return MergeResult(
                success=False,
                merge_id=merge_group.merge_id,
                error_message="Merge group validation failed"
            )
        
        # Execute the merge
        return await self.merge_executor.execute_merge(merge_group)
    
    async def get_merge_status(self, merge_id: str) -> dict:
        """Get the status of a specific merge operation."""
        return await self.merge_executor.get_merge_status(merge_id) 