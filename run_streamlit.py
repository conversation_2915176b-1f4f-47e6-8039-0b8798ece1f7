#!/usr/bin/env python3
"""
Launcher script for the Streamlit Neo4j Query Interface.
"""

import subprocess
import sys
import os

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

def main():
    """Launch the Streamlit application."""
    
    # Check if streamlit is installed
    try:
        import streamlit
        print("✓ Streamlit is installed")
    except ImportError:
        print("❌ Streamlit is not installed. Installing dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements-streamlit.txt"])
        print("✓ Dependencies installed")
    
    # Check if the streamlit app exists
    if not os.path.exists("streamlit_app.py"):
        print("❌ streamlit_app.py not found!")
        return
    
    # Debug: Check if .env file exists and environment variables
    print(f"🔍 Debug: .env file exists: {os.path.exists('.env')}")
    print(f"🔍 Debug: NEO4J_URI env var: {os.getenv('NEO4J_URI', 'Not set')}")
    print(f"🔍 Debug: NEO4J_USERNAME env var: {os.getenv('NEO4J_USERNAME', 'Not set')}")
    print(f"🔍 Debug: NEO4J_PASSWORD env var: {'***' if os.getenv('NEO4J_PASSWORD') else 'Not set'}")

    # Load configuration
    try:
        # Add src to path for imports
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        from src.config.settings import get_settings

        settings = get_settings()
        print(f"✓ Loaded configuration from environment")
        print(f"🔗 Neo4j URI: {settings.neo4j_uri}")
        print(f"👤 Neo4j Username: {settings.neo4j_username}")
        print(f"🔒 Neo4j Password: {'*' * len(settings.neo4j_password) if settings.neo4j_password else 'Not set'}")

    except Exception as e:
        print(f"⚠️  Warning: Could not load configuration: {e}")
        print("📝 Using default connection settings")
        settings = None
    
    print("🚀 Launching Streamlit Neo4j Query Interface...")
    print("📱 The app will open in your default web browser")
    print("🔗 Default URL: http://localhost:8018")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    # Launch streamlit with environment variables
    try:
        env = os.environ.copy()
        if settings:
            env['NEO4J_URI'] = settings.neo4j_uri
            env['NEO4J_USERNAME'] = settings.neo4j_username
            env['NEO4J_PASSWORD'] = settings.neo4j_password
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8018",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ], env=env)
    except KeyboardInterrupt:
        print("\n👋 Streamlit server stopped")
    except Exception as e:
        print(f"❌ Error launching Streamlit: {e}")

if __name__ == "__main__":
    main() 